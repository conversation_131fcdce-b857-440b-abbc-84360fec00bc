# 💘 Linda - Seznamovací Aplikace 2.0

Komplexní seznamovací aplikace navržená pro vážné vztahy i neformální setkání. Moderní, bezpečná a škálovatelná platforma s robustním backendem v Go, frontendem ve Vue.js a databází PostgreSQL.

---

## 🚀 Přehled projektu

### 🎯 Cíle:
- Podpora **vážných vztahů** i **casual dating**
- Cílová skupina: **uživatelé 18+**
- Moderní UX, realtime funkce, důraz na bezpečnost a ochranu soukromí

---

## 🧱 Technologický stack

| Vrstva         | Technologie            |
|----------------|------------------------|
| Backend        | Golang (REST API)      |
| Frontend       | Vue.js 3 + Tailwind CSS|
| Databáze       | PostgreSQL             |
| Cache/Queue    | Redis                  |
| AI Service     | Python FastAPI         |
| Realtime       | WebSockets (gorilla)   |
| Auth           | JWT + refresh tokeny   |
| Kontejnery     | Docker / Docker Compose|
| Monitoring     | Prometheus + Grafana   |

---

## 🧩 Implementované funkce

### ✅ Backend (Go)
- **Modulární architektura**: `handler`, `service`, `repository`
- **JWT autentizace** s refresh tokeny
- **Rate limiting** pomocí Redis
- **CORS middleware**
- **Databázové migrace** s GORM
- **RESTful API** s verzováním `/api/v1`

### ✅ Frontend (Vue.js)
- **Vue 3** s Composition API
- **Tailwind CSS** pro styling
- **Pinia** pro state management
- **Vue Router** pro routing
- **Axios** pro HTTP requesty
- **Toast notifikace**
- **Responsive design**

### ✅ AI Service (Python)
- **Recommendation engine** - doporučování kompatibilních partnerů
- **Content moderation** - moderace obsahu
- **Compatibility analysis** - analýza kompatibility uživatelů
- **PostgreSQL** integrace pro perzistenci dat
- **Redis** pro rychlé cache operace

---

## 🏗️ Architektura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   AI Service    │
│   (Vue.js)      │◄──►│   (Go)          │◄──►│   (Python)      │
│   Port: 3000    │    │   Port: 8080    │    │   Port: 5000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │   Monitoring    │
│   Port: 5432    │    │   Port: 6379    │    │ Prometheus/Graf │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 🚀 Rychlé spuštění

### Předpoklady
- Docker a Docker Compose
- Git

### Instalace a spuštění

1. **Klonování repozitáře**
```bash
git clone <repository-url>
cd linda
```

2. **Spuštění všech služeb**
```bash
docker compose up -d --build
```

3. **Ověření běhu služeb**
```bash
docker compose ps
```

### Přístup k aplikaci

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **AI Service**: http://localhost:5000
- **Grafana**: http://localhost:3001
- **Prometheus**: http://localhost:9090



---

## 📱 Funkce aplikace

### 🔐 Autentizace
- Registrace s e-mailem a heslem
- Přihlášení s JWT tokeny
- Automatické obnovení tokenů
- Bezpečné odhlášení

### 👤 Profily
- Nahrávání až 6 fotek
- Detailní profil (věk, pohlaví, orientace, bio)
- Správa zájmů a preferencí
- Nastavení soukromí

### 🔍 Objevování
- Swipe interface pro lajkování
- Filtry podle věku, vzdálenosti, pohlaví
- Superlike funkce
- AI doporučení kompatibilních partnerů

### 💕 Párování
- Automatické vytvoření shody při vzájemném lajku
- Přehled všech shod
- Možnost zrušení shody

### 💬 Chat
- Realtime zprávy mezi spárovanými uživateli
- Indikátory čtení zpráv
- Typing indikátory
- Historie konverzací

### ⚙️ Nastavení
- Správa účtu a hesla
- Nastavení soukromí
- Notifikační preference
- Možnost smazání účtu

---

## 🗃️ Databázová struktura

```sql
-- Uživatelé
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username TEXT UNIQUE,
  email TEXT UNIQUE,
  password_hash TEXT,
  age INT,
  gender TEXT,
  sexual_orientation TEXT,
  bio TEXT,
  location GEOGRAPHY(Point, 4326),
  looking_for TEXT,
  preferences_age_min INT,
  preferences_age_max INT,
  preferences_gender TEXT,
  is_verified BOOLEAN,
  last_active_at TIMESTAMP,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Fotky
CREATE TABLE photos (
  id SERIAL PRIMARY KEY,
  user_id INT REFERENCES users(id),
  url TEXT,
  is_primary BOOLEAN,
  "order" INT,
  is_nsfw BOOLEAN,
  uploaded_at TIMESTAMP
);

-- Lajky / Superliky
CREATE TABLE likes (
  id SERIAL PRIMARY KEY,
  liker_id INT REFERENCES users(id),
  liked_id INT REFERENCES users(id),
  type TEXT CHECK (type IN ('like', 'superlike')),
  created_at TIMESTAMP
);

-- Páry
CREATE TABLE matches (
  id SERIAL PRIMARY KEY,
  user_a_id INT REFERENCES users(id),
  user_b_id INT REFERENCES users(id),
  chat_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP
);

-- Zprávy
CREATE TABLE messages (
  id SERIAL PRIMARY KEY,
  match_id INT REFERENCES matches(id),
  sender_id INT REFERENCES users(id),
  receiver_id INT REFERENCES users(id),
  content TEXT,
  timestamp TIMESTAMP,
  read BOOLEAN DEFAULT false
);

-- Nahlášení
CREATE TABLE reports (
  id SERIAL PRIMARY KEY,
  reporter_id INT REFERENCES users(id),
  reported_user_id INT REFERENCES users(id),
  reason TEXT,
  type TEXT,
  status TEXT DEFAULT 'pending',
  created_at TIMESTAMP,
  resolved_at TIMESTAMP
);

-- Notifikace
CREATE TABLE notifications (
  id SERIAL PRIMARY KEY,
  user_id INT REFERENCES users(id),
  message TEXT,
  type TEXT,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP
);

-- Admin logy
CREATE TABLE admin_logs (
  id SERIAL PRIMARY KEY,
  admin_id INT REFERENCES users(id),
  action TEXT,
  entity_type TEXT,
  entity_id INT,
  details JSONB,
  timestamp TIMESTAMP
);

-- Zájmy uživatelů
CREATE TABLE user_interests (
  user_id INT REFERENCES users(id),
  interest_tag TEXT
);


```

---

## 🔧 Vývoj

### Lokální vývoj bez Dockeru

#### Backend (Go)
```bash
cd backend
go mod download
go run cmd/server/main.go
```

#### Frontend (Vue.js)
```bash
cd frontend
npm install
npm run dev
```



#### AI Service (Python)
```bash
cd ai
pip install -r requirements.txt
python main.py
```

### Testování

```bash
# Backend testy
cd backend
go test ./...

# Frontend testy
cd frontend
npm run test



# AI Service testy
cd ai
python -m pytest
```

---

## 📊 Monitoring a Logy

### Prometheus Metriky
- HTTP request duration a count
- Database connection pool stats
- Redis operations
- Custom business metriky

### Grafana Dashboards
- System overview
- Application performance
- User activity
- Error rates

### Logy
- Strukturované JSON logy
- Centralizované logování
- Error tracking
- Audit trail

---

## 🔒 Bezpečnost

### Implementované bezpečnostní opatření
- **JWT autentizace** s refresh tokeny
- **Rate limiting** proti spam útokům
- **CORS** konfigurace
- **Input validation** na všech endpointech
- **SQL injection** ochrana pomocí ORM
- **XSS** ochrana v frontend
- **HTTPS** ready konfigurace

### Plánovaná vylepšení
- OAuth2 integrace (Google, Apple)
- 2FA autentizace
- Content Security Policy
- API rate limiting per user
- Audit logging
- Penetration testing

---

## 🚀 Deployment

### Production Environment
```bash
# Build production images
docker compose -f docker-compose.prod.yml build

# Deploy with production config
docker compose -f docker-compose.prod.yml up -d
```

### Environment Variables
```bash
# Backend
DATABASE_URL=********************************/dbname
REDIS_URL=redis://host:6379
JWT_SECRET=your-super-secret-key
ENVIRONMENT=production



# AI Service
AI_DATABASE_URL=********************************/dbname
OPENAI_API_KEY=your-openai-key
```

---

## 🤝 Přispívání

1. Fork repozitář
2. Vytvořte feature branch (`git checkout -b feature/amazing-feature`)
3. Commit změny (`git commit -m 'Add amazing feature'`)
4. Push do branch (`git push origin feature/amazing-feature`)
5. Otevřete Pull Request

### Coding Standards
- **Go**: Použijte `gofmt` a `golint`
- **Vue.js**: Použijte ESLint a Prettier
- **Python**: Použijte Black a Flake8
- **Commit messages**: Conventional Commits format

---

## 📝 Licence

Tento projekt je licencován pod BSD licencí - viz [LICENSE](LICENSE) soubor pro detaily.

---

## 👥 Tým

- **Backend Developer**: Go, PostgreSQL, Redis
- **Frontend Developer**: Vue.js, Tailwind CSS
- **AI Engineer**: Python, FastAPI, ML
- **DevOps Engineer**: Docker, Monitoring

---

## 📞 Kontakt

- **Email**: <EMAIL>
- **Website**: https://linda-dating.com
- **Support**: <EMAIL>

---

## 🎯 Roadmapa

### Q1 2024
- [x] MVP implementace
- [x] AI doporučení
- [ ] Mobile aplikace (Flutter)
- [ ] Push notifikace

### Q2 2024
- [ ] Prémiové členství
- [ ] Video chat
- [ ] Advanced AI features
- [ ] Social media integrace

### Q3 2024
- [ ] Machine learning optimalizace
- [ ] Internationalization
- [ ] Advanced analytics
- [ ] API pro třetí strany

---

*Vytvořeno s ❤️ pro skutečné spojení lidí*
