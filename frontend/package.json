{"name": "linda-dating-frontend", "version": "2.0.0", "description": "Vue.js frontend for <PERSON>pp", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^10.5.0", "axios": "^1.6.2", "lucide-vue-next": "^0.511.0", "pinia": "^2.1.7", "socket.io-client": "^4.7.4", "swiper": "^11.0.5", "vue": "^3.3.8", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}}