@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #ec4899;
  --primary-dark: #db2777;
  --secondary-color: #8b5cf6;
  --accent-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background: #ffffff;
  --surface: #f9fafb;
  --border: #e5e7eb;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
}

.btn-primary {
  @apply bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-outline {
  @apply border-2 border-pink-500 text-pink-500 hover:bg-pink-500 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
}

.input-field {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent outline-none transition-all duration-200;
}

.gradient-text {
  @apply bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent;
}

.swipe-card {
  @apply relative bg-white rounded-2xl shadow-lg overflow-hidden cursor-grab active:cursor-grabbing;
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.swipe-card:hover {
  transform: translateY(-4px);
}

.chat-bubble {
  @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl;
}

.chat-bubble.sent {
  @apply bg-pink-500 text-white ml-auto;
}

.chat-bubble.received {
  @apply bg-gray-200 text-gray-800;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* Modern animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes float-delayed {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-15px) rotate(-180deg);
  }
}

@keyframes float-slow {

  0%,
  100% {
    transform: translateY(0px) scale(1);
  }

  50% {
    transform: translateY(-10px) scale(1.1);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

/* Enhanced heart animations */
.heart-float-1 {
  animation: float 6s ease-in-out infinite;
}

.heart-float-2 {
  animation: float 7s ease-in-out infinite;
}

.heart-float-3 {
  animation: float 8s ease-in-out infinite;
}

.heart-float-4 {
  animation: float 5s ease-in-out infinite;
}

.heart-float-5 {
  animation: float 9s ease-in-out infinite;
}

.heart-float-6 {
  animation: float 6.5s ease-in-out infinite;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient borders */
.gradient-border {
  position: relative;
  background: linear-gradient(45deg, #ec4899, #8b5cf6);
  padding: 2px;
  border-radius: 12px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: white;
  border-radius: 10px;
}