import axios from 'axios'
import { useToast } from 'vue-toastification'

const toast = useToast()

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('token')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          toast.error('Nemáte oprávnění k této akci')
          break
        case 404:
          toast.error('Pož<PERSON><PERSON><PERSON> zdroj nebyl nalezen')
          break
        case 422:
          toast.error(data.error || 'Neplatná data')
          break
        case 429:
          toast.error('<PERSON><PERSON><PERSON><PERSON><PERSON> mnoho požadavků. Zkuste to později.')
          break
        case 500:
          toast.error('Chyba serveru. Zkuste to později.')
          break
        default:
          toast.error(data.error || 'Došlo k neočekávané chybě')
      }
    } else if (error.request) {
      toast.error('Problém s připojením k serveru')
    } else {
      toast.error('Došlo k neočekávané chybě')
    }
    
    return Promise.reject(error)
  }
)

export default api
