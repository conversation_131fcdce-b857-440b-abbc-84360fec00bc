<template>
  <Transition
    enter-active-class="transform ease-out duration-300 transition"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="show"
      class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <!-- Success Icon -->
            <CheckCircleIcon 
              v-if="type === 'success'" 
              class="h-6 w-6 text-green-400" 
              aria-hidden="true" 
            />
            <!-- Error Icon -->
            <XCircleIcon 
              v-else-if="type === 'error'" 
              class="h-6 w-6 text-red-400" 
              aria-hidden="true" 
            />
            <!-- Warning Icon -->
            <ExclamationTriangleIcon 
              v-else-if="type === 'warning'" 
              class="h-6 w-6 text-yellow-400" 
              aria-hidden="true" 
            />
            <!-- Info Icon -->
            <InformationCircleIcon 
              v-else 
              class="h-6 w-6 text-blue-400" 
              aria-hidden="true" 
            />
          </div>
          
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p class="text-sm font-medium text-gray-900">{{ title }}</p>
            <p v-if="message" class="mt-1 text-sm text-gray-500">{{ message }}</p>
            
            <!-- Action buttons -->
            <div v-if="actions && actions.length > 0" class="mt-3 flex space-x-2">
              <button
                v-for="action in actions"
                :key="action.label"
                @click="action.handler"
                class="bg-white rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2"
                :class="action.primary 
                  ? 'text-pink-600 hover:text-pink-500 focus:ring-pink-500' 
                  : 'text-gray-700 hover:text-gray-500 focus:ring-gray-500'"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="close"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500"
            >
              <span class="sr-only">Zavřít</span>
              <XMarkIcon class="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Progress bar for auto-dismiss -->
      <div 
        v-if="autoDismiss && duration > 0"
        class="h-1 bg-gray-200"
      >
        <div 
          class="h-full transition-all ease-linear"
          :class="progressBarColor"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
  </Transition>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'NotificationCard',
  components: {
    CheckCircleIcon,
    XCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XMarkIcon
  },
  props: {
    type: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    title: {
      type: String,
      required: true
    },
    message: {
      type: String,
      default: ''
    },
    actions: {
      type: Array,
      default: () => []
    },
    autoDismiss: {
      type: Boolean,
      default: true
    },
    duration: {
      type: Number,
      default: 5000
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const show = ref(true)
    const progress = ref(100)
    let timer = null
    let progressTimer = null

    const progressBarColor = computed(() => {
      const colors = {
        success: 'bg-green-400',
        error: 'bg-red-400',
        warning: 'bg-yellow-400',
        info: 'bg-blue-400'
      }
      return colors[props.type]
    })

    const close = () => {
      show.value = false
      setTimeout(() => {
        emit('close')
      }, 300)
    }

    const startAutoDismiss = () => {
      if (props.autoDismiss && props.duration > 0) {
        // Progress bar animation
        const interval = 50
        const step = (interval / props.duration) * 100
        
        progressTimer = setInterval(() => {
          progress.value -= step
          if (progress.value <= 0) {
            clearInterval(progressTimer)
          }
        }, interval)

        // Auto close
        timer = setTimeout(() => {
          close()
        }, props.duration)
      }
    }

    onMounted(() => {
      startAutoDismiss()
    })

    onUnmounted(() => {
      if (timer) clearTimeout(timer)
      if (progressTimer) clearInterval(progressTimer)
    })

    return {
      show,
      progress,
      progressBarColor,
      close
    }
  }
}
</script>
