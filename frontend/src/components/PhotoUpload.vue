<template>
  <div class="photo-upload">
    <!-- Photo Grid -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <div 
        v-for="(photo, index) in photos" 
        :key="photo.id || index"
        class="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group"
      >
        <img 
          :src="photo.url || photo.preview" 
          :alt="`Photo ${index + 1}`"
          class="w-full h-full object-cover"
        />
        
        <!-- Primary badge -->
        <div v-if="photo.is_primary" class="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
          Hlavní
        </div>
        
        <!-- Actions overlay -->
        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
          <button 
            v-if="!photo.is_primary"
            @click="setPrimary(index)"
            class="bg-white text-gray-800 px-3 py-1 rounded-full text-sm hover:bg-gray-100"
          >
            Nastavit jako hlav<PERSON>
          </button>
          <button 
            @click="removePhoto(index)"
            class="bg-red-500 text-white px-3 py-1 rounded-full text-sm hover:bg-red-600"
          >
            Smazat
          </button>
        </div>
      </div>
      
      <!-- Add photo slots -->
      <div 
        v-for="n in (6 - photos.length)" 
        :key="`empty-${n}`"
        @click="triggerUpload"
        class="aspect-square bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:border-pink-500 hover:bg-pink-50 transition-colors"
      >
        <div class="text-center text-gray-500">
          <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          <span class="text-sm">Přidat foto</span>
        </div>
      </div>
    </div>
    
    <!-- Upload input -->
    <input 
      ref="fileInput"
      type="file" 
      accept="image/*" 
      multiple
      @change="handleFileSelect"
      class="hidden"
    />
    
    <!-- Upload progress -->
    <div v-if="uploading" class="mb-4">
      <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
        <span>Nahrávání fotek...</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-pink-500 h-2 rounded-full transition-all duration-300"
          :style="{ width: uploadProgress + '%' }"
        ></div>
      </div>
    </div>
    
    <!-- Tips -->
    <div class="text-sm text-gray-600">
      <h4 class="font-medium mb-2">Tipy pro skvělé fotky:</h4>
      <ul class="space-y-1 text-xs">
        <li>• Použijte jasné, kvalitní fotky</li>
        <li>• Usmívejte se a buďte přirození</li>
        <li>• Ukažte své zájmy a koníčky</li>
        <li>• Vyhněte se skupinovým fotkám</li>
        <li>• Maximálně 6 fotek</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

export default {
  name: 'PhotoUpload',
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const toast = useToast()
    const fileInput = ref(null)
    const uploading = ref(false)
    const uploadProgress = ref(0)

    const photos = ref([...props.modelValue])

    const triggerUpload = () => {
      if (photos.value.length >= 6) {
        toast.warning('Můžete mít maximálně 6 fotek')
        return
      }
      fileInput.value?.click()
    }

    const handleFileSelect = async (event) => {
      const files = Array.from(event.target.files)
      
      if (photos.value.length + files.length > 6) {
        toast.warning('Můžete mít maximálně 6 fotek')
        return
      }

      for (const file of files) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
          toast.error(`Soubor ${file.name} je příliš velký (max 5MB)`)
          continue
        }

        if (!file.type.startsWith('image/')) {
          toast.error(`Soubor ${file.name} není obrázek`)
          continue
        }

        await uploadPhoto(file)
      }

      // Clear input
      event.target.value = ''
    }

    const uploadPhoto = async (file) => {
      try {
        uploading.value = true
        uploadProgress.value = 0

        // Create preview
        const preview = URL.createObjectURL(file)
        const tempPhoto = {
          id: Date.now(),
          preview,
          uploading: true
        }
        
        photos.value.push(tempPhoto)
        emit('update:modelValue', photos.value)

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          uploadProgress.value += 10
          if (uploadProgress.value >= 90) {
            clearInterval(progressInterval)
          }
        }, 100)

        // Upload to server
        const formData = new FormData()
        formData.append('photo', file)

        const response = await api.post('/users/photos', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        // Update photo with server response
        const photoIndex = photos.value.findIndex(p => p.id === tempPhoto.id)
        if (photoIndex !== -1) {
          photos.value[photoIndex] = response.data
          emit('update:modelValue', photos.value)
        }

        uploadProgress.value = 100
        toast.success('Fotka byla nahrána')

      } catch (error) {
        // Remove failed upload
        photos.value = photos.value.filter(p => p.id !== tempPhoto.id)
        emit('update:modelValue', photos.value)
        toast.error('Chyba při nahrávání fotky')
      } finally {
        uploading.value = false
        uploadProgress.value = 0
      }
    }

    const removePhoto = async (index) => {
      const photo = photos.value[index]
      
      try {
        if (photo.id && !photo.preview) {
          await api.delete(`/users/photos/${photo.id}`)
        }
        
        photos.value.splice(index, 1)
        emit('update:modelValue', photos.value)
        toast.success('Fotka byla smazána')
      } catch (error) {
        toast.error('Chyba při mazání fotky')
      }
    }

    const setPrimary = async (index) => {
      const photo = photos.value[index]
      
      try {
        await api.put(`/users/photos/${photo.id}/primary`)
        
        // Update primary status
        photos.value.forEach(p => p.is_primary = false)
        photos.value[index].is_primary = true
        
        emit('update:modelValue', photos.value)
        toast.success('Hlavní fotka byla nastavena')
      } catch (error) {
        toast.error('Chyba při nastavování hlavní fotky')
      }
    }

    return {
      photos,
      fileInput,
      uploading,
      uploadProgress,
      triggerUpload,
      handleFileSelect,
      removePhoto,
      setPrimary
    }
  }
}
</script>

<style scoped>
.photo-upload {
  @apply w-full;
}
</style>
