<template>
  <TransitionRoot as="template" :show="show">
    <Dialog as="div" class="relative z-50" @close="handleClose">
      <!-- Backdrop -->
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <!-- Modal panel -->
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel 
              class="relative transform overflow-hidden rounded-2xl bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:p-6"
              :class="sizeClass"
            >
              <!-- Close button -->
              <div v-if="showCloseButton" class="absolute right-0 top-0 pr-4 pt-4">
                <button
                  type="button"
                  class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2"
                  @click="handleClose"
                >
                  <span class="sr-only">Zavřít</span>
                  <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                </button>
              </div>

              <!-- Icon -->
              <div v-if="icon" class="mx-auto flex h-12 w-12 items-center justify-center rounded-full mb-4"
                   :class="iconBgClass">
                <component :is="icon" class="h-6 w-6" :class="iconColorClass" aria-hidden="true" />
              </div>

              <!-- Title -->
              <div v-if="title" class="text-center sm:text-left">
                <DialogTitle as="h3" class="text-lg font-semibold leading-6 text-gray-900 mb-2">
                  {{ title }}
                </DialogTitle>
              </div>

              <!-- Content -->
              <div class="mt-3 text-center sm:text-left">
                <div v-if="description" class="text-sm text-gray-500 mb-4">
                  {{ description }}
                </div>
                
                <!-- Slot content -->
                <slot />
              </div>

              <!-- Actions -->
              <div v-if="$slots.actions || actions.length > 0" 
                   class="mt-5 sm:mt-6 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
                
                <!-- Slot actions -->
                <slot name="actions" />
                
                <!-- Default actions -->
                <template v-if="!$slots.actions">
                  <button
                    v-for="action in actions"
                    :key="action.label"
                    type="button"
                    class="inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm sm:w-auto transition-colors duration-200"
                    :class="action.variant === 'primary' 
                      ? 'bg-pink-600 text-white hover:bg-pink-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600'
                      : action.variant === 'danger'
                      ? 'bg-red-600 text-white hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600'
                      : 'bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50'"
                    @click="action.handler"
                    :disabled="action.loading"
                  >
                    <LoadingSpinner v-if="action.loading" size="sm" class="mr-2" />
                    {{ action.label }}
                  </button>
                </template>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script>
import { computed } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'
import LoadingSpinner from './LoadingSpinner.vue'

export default {
  name: 'Modal',
  components: {
    Dialog,
    DialogPanel,
    DialogTitle,
    TransitionChild,
    TransitionRoot,
    XMarkIcon,
    LoadingSpinner
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl', '2xl'].includes(value)
    },
    icon: {
      type: [Object, Function],
      default: null
    },
    iconType: {
      type: String,
      default: 'info',
      validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    showCloseButton: {
      type: Boolean,
      default: true
    },
    closeOnBackdrop: {
      type: Boolean,
      default: true
    },
    actions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close'],
  setup(props, { emit }) {
    const sizeClass = computed(() => {
      const sizes = {
        sm: 'sm:max-w-sm',
        md: 'sm:max-w-md',
        lg: 'sm:max-w-lg',
        xl: 'sm:max-w-xl',
        '2xl': 'sm:max-w-2xl'
      }
      return sizes[props.size]
    })

    const iconBgClass = computed(() => {
      const classes = {
        success: 'bg-green-100',
        error: 'bg-red-100',
        warning: 'bg-yellow-100',
        info: 'bg-blue-100'
      }
      return classes[props.iconType]
    })

    const iconColorClass = computed(() => {
      const classes = {
        success: 'text-green-600',
        error: 'text-red-600',
        warning: 'text-yellow-600',
        info: 'text-blue-600'
      }
      return classes[props.iconType]
    })

    const handleClose = () => {
      if (props.closeOnBackdrop) {
        emit('close')
      }
    }

    return {
      sizeClass,
      iconBgClass,
      iconColorClass,
      handleClose
    }
  }
}
</script>
