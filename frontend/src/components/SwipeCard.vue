<template>
  <div 
    ref="cardElement"
    class="swipe-card"
    :style="cardStyle"
    @mousedown="startDrag"
    @touchstart="startDrag"
  >
    <!-- User Photo -->
    <div class="relative h-96 overflow-hidden">
      <img 
        :src="user.photos?.[currentPhotoIndex]?.url || '/default-avatar.png'" 
        :alt="user.username"
        class="w-full h-full object-cover"
        @click="nextPhoto"
      />
      
      <!-- Photo indicators -->
      <div v-if="user.photos?.length > 1" class="absolute top-4 left-4 right-4 flex space-x-1">
        <div 
          v-for="(photo, index) in user.photos" 
          :key="index"
          class="flex-1 h-1 rounded-full"
          :class="index === currentPhotoIndex ? 'bg-white' : 'bg-white/30'"
        ></div>
      </div>
      
      <!-- Age -->
      <div class="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
        {{ user.age }} let
      </div>
      
      <!-- Distance -->
      <div v-if="user.distance" class="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
        {{ user.distance }} km
      </div>
      
      <!-- Swipe indicators -->
      <div 
        v-if="swipeDirection === 'left'"
        class="absolute inset-0 bg-red-500/20 flex items-center justify-center"
      >
        <div class="bg-red-500 text-white px-6 py-3 rounded-full font-bold text-xl transform rotate-12">
          PASS
        </div>
      </div>
      
      <div 
        v-if="swipeDirection === 'right'"
        class="absolute inset-0 bg-green-500/20 flex items-center justify-center"
      >
        <div class="bg-green-500 text-white px-6 py-3 rounded-full font-bold text-xl transform -rotate-12">
          LIKE
        </div>
      </div>
      
      <div 
        v-if="swipeDirection === 'up'"
        class="absolute inset-0 bg-blue-500/20 flex items-center justify-center"
      >
        <div class="bg-blue-500 text-white px-6 py-3 rounded-full font-bold text-xl">
          SUPER LIKE
        </div>
      </div>
    </div>
    
    <!-- User Info -->
    <div class="p-4">
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-xl font-bold text-gray-900">{{ user.username }}</h3>
        <div v-if="user.is_verified" class="text-blue-500">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      
      <p v-if="user.bio" class="text-gray-600 text-sm mb-3 line-clamp-2">
        {{ user.bio }}
      </p>
      
      <!-- Interests -->
      <div v-if="user.interests?.length" class="flex flex-wrap gap-1 mb-3">
        <span 
          v-for="interest in user.interests.slice(0, 3)" 
          :key="interest.id"
          class="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-xs"
        >
          {{ interest.interest_tag }}
        </span>
        <span v-if="user.interests.length > 3" class="text-gray-500 text-xs">
          +{{ user.interests.length - 3 }} dalších
        </span>
      </div>
      
      <!-- Action buttons -->
      <div class="flex justify-center space-x-4 mt-4">
        <button 
          @click="$emit('pass')"
          class="w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
        >
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <button 
          @click="$emit('superlike')"
          class="w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center transition-colors"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        </button>
        
        <button 
          @click="$emit('like')"
          class="w-12 h-12 bg-pink-500 hover:bg-pink-600 text-white rounded-full flex items-center justify-center transition-colors"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SwipeCard',
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  emits: ['like', 'pass', 'superlike', 'swipe'],
  setup(props, { emit }) {
    const cardElement = ref(null)
    const currentPhotoIndex = ref(0)
    const isDragging = ref(false)
    const startX = ref(0)
    const startY = ref(0)
    const currentX = ref(0)
    const currentY = ref(0)
    const swipeDirection = ref(null)

    const cardStyle = computed(() => {
      if (!isDragging.value) return {}
      
      const deltaX = currentX.value - startX.value
      const deltaY = currentY.value - startY.value
      const rotation = deltaX * 0.1
      
      return {
        transform: `translate(${deltaX}px, ${deltaY}px) rotate(${rotation}deg)`,
        opacity: Math.max(0.5, 1 - Math.abs(deltaX) / 300)
      }
    })

    const startDrag = (event) => {
      isDragging.value = true
      const clientX = event.clientX || event.touches[0].clientX
      const clientY = event.clientY || event.touches[0].clientY
      
      startX.value = clientX
      startY.value = clientY
      currentX.value = clientX
      currentY.value = clientY
      
      document.addEventListener('mousemove', onDrag)
      document.addEventListener('mouseup', endDrag)
      document.addEventListener('touchmove', onDrag)
      document.addEventListener('touchend', endDrag)
    }

    const onDrag = (event) => {
      if (!isDragging.value) return
      
      event.preventDefault()
      const clientX = event.clientX || event.touches[0].clientX
      const clientY = event.clientY || event.touches[0].clientY
      
      currentX.value = clientX
      currentY.value = clientY
      
      const deltaX = currentX.value - startX.value
      const deltaY = currentY.value - startY.value
      
      // Determine swipe direction
      if (Math.abs(deltaX) > 50) {
        swipeDirection.value = deltaX > 0 ? 'right' : 'left'
      } else if (deltaY < -50) {
        swipeDirection.value = 'up'
      } else {
        swipeDirection.value = null
      }
    }

    const endDrag = () => {
      if (!isDragging.value) return
      
      const deltaX = currentX.value - startX.value
      const deltaY = currentY.value - startY.value
      
      // Check if swipe threshold is met
      if (Math.abs(deltaX) > 100) {
        if (deltaX > 0) {
          emit('like')
        } else {
          emit('pass')
        }
        emit('swipe', deltaX > 0 ? 'right' : 'left')
      } else if (deltaY < -100) {
        emit('superlike')
        emit('swipe', 'up')
      }
      
      // Reset
      isDragging.value = false
      swipeDirection.value = null
      currentX.value = startX.value
      currentY.value = startY.value
      
      document.removeEventListener('mousemove', onDrag)
      document.removeEventListener('mouseup', endDrag)
      document.removeEventListener('touchmove', onDrag)
      document.removeEventListener('touchend', endDrag)
    }

    const nextPhoto = () => {
      if (props.user.photos?.length > 1) {
        currentPhotoIndex.value = (currentPhotoIndex.value + 1) % props.user.photos.length
      }
    }

    onMounted(() => {
      // Prevent default drag behavior on images
      if (cardElement.value) {
        cardElement.value.addEventListener('dragstart', (e) => e.preventDefault())
      }
    })

    onUnmounted(() => {
      document.removeEventListener('mousemove', onDrag)
      document.removeEventListener('mouseup', endDrag)
      document.removeEventListener('touchmove', onDrag)
      document.removeEventListener('touchend', endDrag)
    })

    return {
      cardElement,
      currentPhotoIndex,
      cardStyle,
      swipeDirection,
      startDrag,
      nextPhoto
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
