<template>
  <div class="flex items-center justify-center" :class="containerClass">
    <div class="relative">
      <!-- Outer ring -->
      <div 
        class="animate-spin rounded-full border-4 border-gray-200"
        :class="[sizeClass, colorClass]"
      ></div>
      
      <!-- Inner pulsing dot -->
      <div 
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full animate-pulse"
        :class="[dotSizeClass, dotColorClass]"
      ></div>
    </div>
    
    <!-- Loading text -->
    <div v-if="showText" class="ml-3">
      <p class="text-gray-600 font-medium">{{ text }}</p>
      <div class="flex space-x-1 mt-1">
        <div 
          v-for="i in 3" 
          :key="i"
          class="w-1 h-1 bg-pink-500 rounded-full animate-bounce"
          :style="{ animationDelay: `${i * 0.1}s` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'LoadingSpinner',
  props: {
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    color: {
      type: String,
      default: 'pink',
      validator: (value) => ['pink', 'purple', 'blue', 'green', 'gray'].includes(value)
    },
    text: {
      type: String,
      default: 'Načítání...'
    },
    showText: {
      type: Boolean,
      default: false
    },
    fullScreen: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const sizeClass = computed(() => {
      const sizes = {
        sm: 'w-6 h-6',
        md: 'w-8 h-8',
        lg: 'w-12 h-12',
        xl: 'w-16 h-16'
      }
      return sizes[props.size]
    })

    const dotSizeClass = computed(() => {
      const sizes = {
        sm: 'w-2 h-2',
        md: 'w-3 h-3',
        lg: 'w-4 h-4',
        xl: 'w-6 h-6'
      }
      return sizes[props.size]
    })

    const colorClass = computed(() => {
      const colors = {
        pink: 'border-t-pink-500',
        purple: 'border-t-purple-500',
        blue: 'border-t-blue-500',
        green: 'border-t-green-500',
        gray: 'border-t-gray-500'
      }
      return colors[props.color]
    })

    const dotColorClass = computed(() => {
      const colors = {
        pink: 'bg-pink-500',
        purple: 'bg-purple-500',
        blue: 'bg-blue-500',
        green: 'bg-green-500',
        gray: 'bg-gray-500'
      }
      return colors[props.color]
    })

    const containerClass = computed(() => {
      return props.fullScreen 
        ? 'fixed inset-0 bg-white/80 backdrop-blur-sm z-50' 
        : 'py-8'
    })

    return {
      sizeClass,
      dotSizeClass,
      colorClass,
      dotColorClass,
      containerClass
    }
  }
}
</script>
