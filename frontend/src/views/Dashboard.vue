<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold gradient-text">💘 Linda</h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <router-link to="/discover" class="text-gray-600 hover:text-gray-900">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </router-link>
            
            <router-link to="/matches" class="text-gray-600 hover:text-gray-900 relative">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span v-if="matchCount > 0" class="absolute -top-2 -right-2 bg-pink-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {{ matchCount }}
              </span>
            </router-link>
            
            <router-link to="/chat" class="text-gray-600 hover:text-gray-900 relative">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span v-if="unreadMessages > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {{ unreadMessages }}
              </span>
            </router-link>
            
            <div class="relative" ref="profileDropdown">
              <button @click="showProfileMenu = !showProfileMenu" class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                <img 
                  :src="user?.photos?.[0]?.url || '/default-avatar.png'" 
                  :alt="user?.username"
                  class="w-8 h-8 rounded-full object-cover"
                />
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              <div v-if="showProfileMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <router-link to="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Můj profil
                </router-link>
                <router-link to="/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Nastavení
                </router-link>
                <button @click="handleLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Odhlásit se
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Welcome Section -->
      <div class="px-4 py-6 sm:px-0">
        <div class="bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg p-6 text-white mb-6">
          <h1 class="text-2xl font-bold mb-2">
            Vítejte zpět, {{ user?.username }}! 👋
          </h1>
          <p class="text-pink-100">
            Připraveni najít nové spojení?
          </p>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="card text-center">
            <div class="text-3xl font-bold text-pink-500 mb-2">{{ stats.profileViews }}</div>
            <div class="text-gray-600">Zobrazení profilu</div>
          </div>
          
          <div class="card text-center">
            <div class="text-3xl font-bold text-purple-500 mb-2">{{ stats.likes }}</div>
            <div class="text-gray-600">Získané lajky</div>
          </div>
          
          <div class="card text-center">
            <div class="text-3xl font-bold text-indigo-500 mb-2">{{ stats.matches }}</div>
            <div class="text-gray-600">Nové shody</div>
          </div>
          
          <div class="card text-center">
            <div class="text-3xl font-bold text-green-500 mb-2">{{ stats.messages }}</div>
            <div class="text-gray-600">Zprávy</div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <router-link to="/discover" class="card hover:shadow-md transition-shadow cursor-pointer">
            <div class="flex items-center">
              <div class="bg-pink-100 p-3 rounded-full mr-4">
                <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900">Objevovat</h3>
                <p class="text-gray-600">Najděte nové lidi</p>
              </div>
            </div>
          </router-link>

          <router-link to="/matches" class="card hover:shadow-md transition-shadow cursor-pointer">
            <div class="flex items-center">
              <div class="bg-purple-100 p-3 rounded-full mr-4">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900">Shody</h3>
                <p class="text-gray-600">Vaše spojení</p>
              </div>
            </div>
          </router-link>

          <router-link to="/chat" class="card hover:shadow-md transition-shadow cursor-pointer">
            <div class="flex items-center">
              <div class="bg-indigo-100 p-3 rounded-full mr-4">
                <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900">Zprávy</h3>
                <p class="text-gray-600">Chatujte s lidmi</p>
              </div>
            </div>
          </router-link>
        </div>

        <!-- Recent Activity -->
        <div class="card">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Nedávná aktivita</h2>
          
          <div v-if="recentActivity.length === 0" class="text-center py-8 text-gray-500">
            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p>Zatím žádná aktivita</p>
            <p class="text-sm">Začněte objevovat nové lidi!</p>
          </div>

          <div v-else class="space-y-4">
            <div v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
              <div class="flex-shrink-0">
                <img :src="activity.avatar" :alt="activity.user" class="w-10 h-10 rounded-full object-cover">
              </div>
              <div class="flex-1">
                <p class="text-sm text-gray-900">
                  <span class="font-medium">{{ activity.user }}</span>
                  {{ activity.action }}
                </p>
                <p class="text-xs text-gray-500">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const toast = useToast()

    const showProfileMenu = ref(false)
    const profileDropdown = ref(null)
    
    const stats = ref({
      profileViews: 42,
      likes: 18,
      matches: 5,
      messages: 12
    })

    const matchCount = ref(3)
    const unreadMessages = ref(2)
    
    const recentActivity = ref([
      {
        id: 1,
        user: 'Anna',
        action: 'vás lajkla',
        time: 'před 2 hodinami',
        avatar: '/default-avatar.png'
      },
      {
        id: 2,
        user: 'Petr',
        action: 'vám napsal zprávu',
        time: 'před 4 hodinami',
        avatar: '/default-avatar.png'
      }
    ])

    const handleLogout = async () => {
      await authStore.logout()
      toast.success('Úspěšně odhlášen')
      router.push('/')
    }

    const handleClickOutside = (event) => {
      if (profileDropdown.value && !profileDropdown.value.contains(event.target)) {
        showProfileMenu.value = false
      }
    }

    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      user: authStore.user,
      showProfileMenu,
      profileDropdown,
      stats,
      matchCount,
      unreadMessages,
      recentActivity,
      handleLogout
    }
  }
}
</script>
