<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <router-link to="/dashboard" class="text-2xl font-bold gradient-text">💘 Linda</router-link>
          <h1 class="text-xl font-semibold text-gray-900">Shody</h1>
          <div class="w-20"></div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 px-4">
      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card text-center">
          <div class="text-3xl font-bold text-pink-500 mb-2">{{ matches.length }}</div>
          <div class="text-gray-600">Celkem shod</div>
        </div>
        
        <div class="card text-center">
          <div class="text-3xl font-bold text-purple-500 mb-2">{{ newMatches }}</div>
          <div class="text-gray-600">Nové shody</div>
        </div>
        
        <div class="card text-center">
          <div class="text-3xl font-bold text-indigo-500 mb-2">{{ activeChats }}</div>
          <div class="text-gray-600">Aktivní chaty</div>
        </div>
      </div>

      <!-- Matches Grid -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="loading-spinner"></div>
      </div>

      <div v-else-if="matches.length === 0" class="text-center py-12">
        <div class="text-6xl mb-4">💔</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Zatím žádné shody</h2>
        <p class="text-gray-600 mb-6">Začněte objevovat nové lidi a najděte své spojení!</p>
        <router-link to="/discover" class="btn-primary">
          Začít objevovat
        </router-link>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="match in matches" 
          :key="match.id"
          class="card hover:shadow-lg transition-shadow cursor-pointer group"
          @click="openChat(match)"
        >
          <!-- Match Photo -->
          <div class="relative mb-4">
            <img 
              :src="getMatchUser(match).photos?.[0]?.url || '/default-avatar.png'" 
              :alt="getMatchUser(match).username"
              class="w-full h-48 object-cover rounded-lg"
            />
            
            <!-- New match badge -->
            <div v-if="isNewMatch(match)" class="absolute top-2 right-2 bg-pink-500 text-white text-xs px-2 py-1 rounded-full">
              Nová shoda!
            </div>

            <!-- Online status -->
            <div 
              v-if="isUserOnline(getMatchUser(match).id)"
              class="absolute bottom-2 right-2 w-4 h-4 bg-green-500 border-2 border-white rounded-full"
            ></div>
          </div>

          <!-- Match Info -->
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-1">
              {{ getMatchUser(match).username }}, {{ getMatchUser(match).age }}
            </h3>
            <p class="text-gray-600 text-sm line-clamp-2">
              {{ getMatchUser(match).bio }}
            </p>
          </div>

          <!-- Last Message -->
          <div v-if="match.messages && match.messages.length > 0" class="mb-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <p class="text-sm text-gray-700 line-clamp-2">
                {{ match.messages[0].content }}
              </p>
              <p class="text-xs text-gray-500 mt-1">
                {{ formatTime(match.messages[0].timestamp) }}
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex space-x-2">
            <button 
              @click.stop="openChat(match)"
              class="btn-primary flex-1 text-sm"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Napsat
            </button>
            
            <button 
              @click.stop="viewProfile(getMatchUser(match))"
              class="btn-secondary text-sm"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </button>
            
            <button 
              @click.stop="showUnmatchModal(match)"
              class="text-gray-400 hover:text-red-500 p-2"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Profile Modal -->
    <div v-if="selectedUser" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Profile Header -->
        <div class="relative">
          <img 
            :src="selectedUser.photos?.[0]?.url || '/default-avatar.png'" 
            :alt="selectedUser.username"
            class="w-full h-64 object-cover"
          />
          <button 
            @click="selectedUser = null"
            class="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Profile Content -->
        <div class="p-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">
            {{ selectedUser.username }}, {{ selectedUser.age }}
          </h2>
          
          <p class="text-gray-600 mb-4">{{ selectedUser.bio }}</p>

          <!-- Interests -->
          <div v-if="selectedUser.interests && selectedUser.interests.length > 0" class="mb-4">
            <h3 class="font-semibold text-gray-900 mb-2">Zájmy</h3>
            <div class="flex flex-wrap gap-2">
              <span 
                v-for="interest in selectedUser.interests" 
                :key="interest"
                class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm"
              >
                {{ interest }}
              </span>
            </div>
          </div>

          <!-- Additional Photos -->
          <div v-if="selectedUser.photos && selectedUser.photos.length > 1" class="mb-4">
            <h3 class="font-semibold text-gray-900 mb-2">Další fotky</h3>
            <div class="grid grid-cols-3 gap-2">
              <img 
                v-for="(photo, index) in selectedUser.photos.slice(1)" 
                :key="index"
                :src="photo.url" 
                :alt="`Fotka ${index + 2}`"
                class="w-full h-20 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Unmatch Modal -->
    <div v-if="unmatchCandidate" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl p-6 max-w-sm w-full text-center">
        <div class="text-4xl mb-4">😢</div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">Zrušit shodu?</h2>
        <p class="text-gray-600 mb-6">
          Opravdu chcete zrušit shodu s {{ getMatchUser(unmatchCandidate).username }}? 
          Tuto akci nelze vrátit zpět.
        </p>
        
        <div class="flex space-x-4">
          <button @click="unmatchCandidate = null" class="btn-secondary flex-1">
            Zrušit
          </button>
          <button @click="confirmUnmatch" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg flex-1">
            Zrušit shodu
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

export default {
  name: 'Matches',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const toast = useToast()

    const loading = ref(false)
    const matches = ref([])
    const selectedUser = ref(null)
    const unmatchCandidate = ref(null)
    const onlineUsers = ref(new Set())

    const newMatches = computed(() => {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      return matches.value.filter(match => new Date(match.created_at) > oneDayAgo).length
    })

    const activeChats = computed(() => {
      return matches.value.filter(match => 
        match.messages && match.messages.length > 0
      ).length
    })

    const loadMatches = async () => {
      try {
        loading.value = true
        const response = await api.get('/matching/matches')
        matches.value = response.data.matches
      } catch (error) {
        toast.error('Chyba při načítání shod')
      } finally {
        loading.value = false
      }
    }

    const getMatchUser = (match) => {
      const currentUserId = authStore.user?.id
      return match.user_a_id === currentUserId ? match.user_b : match.user_a
    }

    const isNewMatch = (match) => {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      return new Date(match.created_at) > oneDayAgo
    }

    const isUserOnline = (userId) => {
      return onlineUsers.value.has(userId)
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffInHours = (now - date) / (1000 * 60 * 60)

      if (diffInHours < 1) {
        return 'před chvílí'
      } else if (diffInHours < 24) {
        return `před ${Math.floor(diffInHours)} h`
      } else {
        return date.toLocaleDateString('cs-CZ')
      }
    }

    const openChat = (match) => {
      router.push(`/chat/${match.id}`)
    }

    const viewProfile = (user) => {
      selectedUser.value = user
    }

    const showUnmatchModal = (match) => {
      unmatchCandidate.value = match
    }

    const confirmUnmatch = async () => {
      try {
        await api.delete(`/matching/matches/${unmatchCandidate.value.id}`)
        matches.value = matches.value.filter(m => m.id !== unmatchCandidate.value.id)
        toast.success('Shoda byla zrušena')
        unmatchCandidate.value = null
      } catch (error) {
        toast.error('Chyba při rušení shody')
      }
    }

    onMounted(() => {
      loadMatches()
      
      // Simulate some online users
      setTimeout(() => {
        onlineUsers.value.add(1)
        onlineUsers.value.add(3)
        onlineUsers.value.add(5)
      }, 1000)
    })

    return {
      loading,
      matches,
      selectedUser,
      unmatchCandidate,
      newMatches,
      activeChats,
      getMatchUser,
      isNewMatch,
      isUserOnline,
      formatTime,
      openChat,
      viewProfile,
      showUnmatchModal,
      confirmUnmatch
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
