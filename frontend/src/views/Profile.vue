<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <router-link to="/dashboard" class="text-2xl font-bold gradient-text">💘 Linda</router-link>
          <h1 class="text-xl font-semibold text-gray-900">Můj profil</h1>
          <button @click="saveProfile" :disabled="saving" class="btn-primary">
            {{ saving ? 'Ukládám...' : 'Uložit' }}
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-2xl mx-auto py-6 px-4">
      <!-- Profile Photos -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Fotky</h2>
        
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div 
            v-for="(photo, index) in photos" 
            :key="photo.id || index"
            class="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group"
          >
            <img 
              :src="photo.url" 
              :alt="`Fotka ${index + 1}`"
              class="w-full h-full object-cover"
            />
            
            <!-- Primary badge -->
            <div v-if="photo.is_primary" class="absolute top-2 left-2 bg-pink-500 text-white text-xs px-2 py-1 rounded-full">
              Hlavní
            </div>
            
            <!-- Actions overlay -->
            <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
              <button 
                v-if="!photo.is_primary"
                @click="setPrimaryPhoto(photo.id)"
                class="bg-white text-gray-900 px-3 py-1 rounded-full text-sm hover:bg-gray-100"
              >
                Nastavit jako hlavní
              </button>
              <button 
                @click="deletePhoto(photo.id)"
                class="bg-red-500 text-white px-3 py-1 rounded-full text-sm hover:bg-red-600"
              >
                Smazat
              </button>
            </div>
          </div>
          
          <!-- Add photo button -->
          <div 
            v-if="photos.length < 6"
            class="aspect-square bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:border-pink-500 hover:bg-pink-50 transition-colors"
            @click="triggerPhotoUpload"
          >
            <div class="text-center">
              <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              <span class="text-sm text-gray-600">Přidat fotku</span>
            </div>
          </div>
        </div>
        
        <input 
          ref="photoInput" 
          type="file" 
          accept="image/*" 
          class="hidden" 
          @change="handlePhotoUpload"
        />
        
        <p class="text-sm text-gray-600">
          Přidejte až 6 fotek. První fotka bude vaše hlavní profilová fotka.
        </p>
      </div>

      <!-- Basic Info -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Základní informace</h2>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Věk</label>
              <input 
                v-model="profile.age" 
                type="number" 
                min="18" 
                max="100" 
                class="input-field"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Pohlaví</label>
              <select v-model="profile.gender" class="input-field">
                <option value="">Vyberte pohlaví</option>
                <option value="male">Muž</option>
                <option value="female">Žena</option>
                <option value="other">Jiné</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Sexuální orientace</label>
            <select v-model="profile.sexual_orientation" class="input-field">
              <option value="">Vyberte orientaci</option>
              <option value="heterosexual">Heterosexuální</option>
              <option value="homosexual">Homosexuální</option>
              <option value="bisexual">Bisexuální</option>
              <option value="pansexual">Pansexuální</option>
              <option value="other">Jiné</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Hledám</label>
            <select v-model="profile.looking_for" class="input-field">
              <option value="">Co hledáte?</option>
              <option value="relationship">Vážný vztah</option>
              <option value="casual">Neformální setkání</option>
              <option value="friendship">Přátelství</option>
              <option value="networking">Networking</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">O mně</label>
            <textarea 
              v-model="profile.bio" 
              rows="4" 
              class="input-field resize-none"
              placeholder="Napište něco o sobě..."
              maxlength="500"
            ></textarea>
            <div class="text-right text-sm text-gray-500 mt-1">
              {{ profile.bio?.length || 0 }}/500
            </div>
          </div>
        </div>
      </div>

      <!-- Interests -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Zájmy</h2>
        
        <div class="mb-4">
          <div class="flex flex-wrap gap-2 mb-4">
            <span 
              v-for="interest in selectedInterests" 
              :key="interest"
              class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm flex items-center"
            >
              {{ interest }}
              <button 
                @click="removeInterest(interest)"
                class="ml-2 text-pink-600 hover:text-pink-800"
              >
                ×
              </button>
            </span>
          </div>
          
          <div class="flex">
            <input 
              v-model="newInterest" 
              @keyup.enter="addInterest"
              type="text" 
              placeholder="Přidat zájem..."
              class="input-field flex-1 mr-2"
            />
            <button @click="addInterest" class="btn-primary">
              Přidat
            </button>
          </div>
        </div>

        <div class="space-y-2">
          <h3 class="text-sm font-medium text-gray-700">Populární zájmy:</h3>
          <div class="flex flex-wrap gap-2">
            <button 
              v-for="interest in popularInterests" 
              :key="interest"
              @click="toggleInterest(interest)"
              class="px-3 py-1 rounded-full text-sm border transition-colors"
              :class="selectedInterests.includes(interest) 
                ? 'bg-pink-500 text-white border-pink-500' 
                : 'bg-white text-gray-700 border-gray-300 hover:border-pink-500'"
            >
              {{ interest }}
            </button>
          </div>
        </div>
      </div>

      <!-- Preferences -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Preference</h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Věkové rozmezí</label>
            <div class="flex items-center space-x-4">
              <input 
                v-model="profile.preferences_age_min" 
                type="number" 
                min="18" 
                max="100" 
                class="input-field flex-1"
                placeholder="Od"
              />
              <span class="text-gray-500">-</span>
              <input 
                v-model="profile.preferences_age_max" 
                type="number" 
                min="18" 
                max="100" 
                class="input-field flex-1"
                placeholder="Do"
              />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Preferované pohlaví</label>
            <select v-model="profile.preferences_gender" class="input-field">
              <option value="">Všechna pohlaví</option>
              <option value="male">Muži</option>
              <option value="female">Ženy</option>
              <option value="other">Jiné</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Account Settings -->
      <div class="card">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Nastavení účtu</h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Ověřený profil</h3>
              <p class="text-sm text-gray-600">Zvyšte důvěryhodnost svého profilu</p>
            </div>
            <span v-if="profile.is_verified" class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
              Ověřeno ✓
            </span>
            <button v-else class="btn-outline">
              Ověřit profil
            </button>
          </div>

          <div class="border-t pt-4">
            <button class="text-red-600 hover:text-red-800 font-medium">
              Smazat účet
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import api from '@/services/api'
import PhotoUpload from '@/components/PhotoUpload.vue'

export default {
  name: 'Profile',
  components: {
    PhotoUpload
  },
  setup() {
    const authStore = useAuthStore()
    const toast = useToast()

    const saving = ref(false)
    const photoInput = ref(null)
    const newInterest = ref('')

    const profile = ref({
      age: null,
      gender: '',
      sexual_orientation: '',
      bio: '',
      looking_for: '',
      preferences_age_min: 18,
      preferences_age_max: 35,
      preferences_gender: '',
      is_verified: false
    })

    const photos = ref([])
    const selectedInterests = ref([])

    const popularInterests = ref([
      'Cestování', 'Sport', 'Hudba', 'Filmy', 'Čtení', 'Vaření',
      'Fotografie', 'Tanec', 'Umění', 'Příroda', 'Fitness', 'Jóga',
      'Technologie', 'Hry', 'Móda', 'Zvířata'
    ])

    const loadProfile = async () => {
      try {
        const response = await api.get('/users/profile')
        const userData = response.data
        
        profile.value = {
          age: userData.age,
          gender: userData.gender,
          sexual_orientation: userData.sexual_orientation,
          bio: userData.bio,
          looking_for: userData.looking_for,
          preferences_age_min: userData.preferences_age_min,
          preferences_age_max: userData.preferences_age_max,
          preferences_gender: userData.preferences_gender,
          is_verified: userData.is_verified
        }

        photos.value = userData.photos || []
        selectedInterests.value = userData.interests?.map(i => i.interest_tag) || []
      } catch (error) {
        toast.error('Chyba při načítání profilu')
      }
    }

    const saveProfile = async () => {
      try {
        saving.value = true
        await api.put('/users/profile', profile.value)
        toast.success('Profil byl uložen')
        authStore.fetchUser()
      } catch (error) {
        toast.error('Chyba při ukládání profilu')
      } finally {
        saving.value = false
      }
    }

    const triggerPhotoUpload = () => {
      photoInput.value?.click()
    }

    const handlePhotoUpload = async (event) => {
      const file = event.target.files[0]
      if (!file) return

      // In a real app, you would upload to a file storage service
      // For now, we'll simulate with a placeholder URL
      const mockUrl = URL.createObjectURL(file)
      
      try {
        const response = await api.post('/users/photos', {
          url: mockUrl,
          order: photos.value.length
        })
        
        photos.value.push(response.data)
        toast.success('Fotka byla přidána')
      } catch (error) {
        toast.error('Chyba při nahrávání fotky')
      }
    }

    const setPrimaryPhoto = async (photoId) => {
      try {
        await api.put(`/users/photos/${photoId}/primary`)
        photos.value.forEach(photo => {
          photo.is_primary = photo.id === photoId
        })
        toast.success('Hlavní fotka byla nastavena')
      } catch (error) {
        toast.error('Chyba při nastavování hlavní fotky')
      }
    }

    const deletePhoto = async (photoId) => {
      try {
        await api.delete(`/users/photos/${photoId}`)
        photos.value = photos.value.filter(photo => photo.id !== photoId)
        toast.success('Fotka byla smazána')
      } catch (error) {
        toast.error('Chyba při mazání fotky')
      }
    }

    const addInterest = () => {
      if (newInterest.value.trim() && !selectedInterests.value.includes(newInterest.value.trim())) {
        selectedInterests.value.push(newInterest.value.trim())
        newInterest.value = ''
      }
    }

    const removeInterest = (interest) => {
      selectedInterests.value = selectedInterests.value.filter(i => i !== interest)
    }

    const toggleInterest = (interest) => {
      if (selectedInterests.value.includes(interest)) {
        removeInterest(interest)
      } else {
        selectedInterests.value.push(interest)
      }
    }

    onMounted(() => {
      loadProfile()
    })

    return {
      saving,
      photoInput,
      newInterest,
      profile,
      photos,
      selectedInterests,
      popularInterests,
      saveProfile,
      triggerPhotoUpload,
      handlePhotoUpload,
      setPrimaryPhoto,
      deletePhoto,
      addInterest,
      removeInterest,
      toggleInterest
    }
  }
}
</script>
