<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <router-link to="/dashboard" class="text-2xl font-bold gradient-text">💘 Linda</router-link>
          <h1 class="text-xl font-semibold text-gray-900">Objevovat</h1>
          <div class="w-20"></div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-md mx-auto py-6 px-4">
      <!-- Filters -->
      <div class="mb-6">
        <button @click="showFilters = !showFilters" class="w-full btn-outline flex items-center justify-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
          </svg>
          Filtry
        </button>

        <div v-if="showFilters" class="mt-4 card">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Věk</label>
              <div class="flex items-center space-x-4">
                <input v-model="filters.ageMin" type="number" min="18" max="100" class="input-field flex-1" placeholder="Od">
                <span class="text-gray-500">-</span>
                <input v-model="filters.ageMax" type="number" min="18" max="100" class="input-field flex-1" placeholder="Do">
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Vzdálenost (km)</label>
              <input v-model="filters.distance" type="range" min="1" max="100" class="w-full">
              <div class="text-center text-sm text-gray-600">{{ filters.distance }} km</div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Pohlaví</label>
              <select v-model="filters.gender" class="input-field">
                <option value="">Všechna</option>
                <option value="male">Muž</option>
                <option value="female">Žena</option>
                <option value="other">Jiné</option>
              </select>
            </div>

            <button @click="applyFilters" class="w-full btn-primary">
              Použít filtry
            </button>
          </div>
        </div>
      </div>

      <!-- Card Stack -->
      <div class="relative h-96 mb-6">
        <div v-if="loading" class="absolute inset-0 flex items-center justify-center">
          <div class="loading-spinner"></div>
        </div>

        <div v-else-if="users.length === 0" class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="text-6xl mb-4">😔</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Žádní další uživatelé</h3>
            <p class="text-gray-600 mb-4">Zkuste změnit filtry nebo se vraťte později</p>
            <button @click="loadUsers" class="btn-primary">
              Načíst znovu
            </button>
          </div>
        </div>

        <div v-else class="relative h-full">
          <div
            v-for="(user, index) in visibleUsers"
            :key="user.id"
            :style="{ zIndex: users.length - index }"
            class="absolute inset-0 swipe-card"
            :class="{ 'animate-pulse': user.id === currentUser?.id && isAnimating }"
          >
            <!-- User Card -->
            <div class="h-full bg-white rounded-2xl shadow-lg overflow-hidden">
              <!-- Photo -->
              <div class="relative h-3/4">
                <img 
                  :src="user.photos?.[0]?.url || '/default-avatar.png'" 
                  :alt="user.username"
                  class="w-full h-full object-cover"
                />
                
                <!-- Gradient overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                
                <!-- User info overlay -->
                <div class="absolute bottom-4 left-4 right-4 text-white">
                  <h2 class="text-2xl font-bold mb-1">{{ user.username }}, {{ user.age }}</h2>
                  <p class="text-sm opacity-90 mb-2">{{ user.bio }}</p>
                  <div class="flex flex-wrap gap-1">
                    <span 
                      v-for="interest in user.interests?.slice(0, 3)" 
                      :key="interest"
                      class="bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full text-xs"
                    >
                      {{ interest }}
                    </span>
                  </div>
                </div>

                <!-- Photo indicators -->
                <div v-if="user.photos?.length > 1" class="absolute top-4 left-4 right-4 flex space-x-1">
                  <div 
                    v-for="(photo, photoIndex) in user.photos" 
                    :key="photoIndex"
                    class="flex-1 h-1 bg-white/30 rounded-full"
                    :class="{ 'bg-white': photoIndex === 0 }"
                  ></div>
                </div>
              </div>

              <!-- Action buttons -->
              <div class="h-1/4 flex items-center justify-center space-x-6 px-6">
                <button 
                  @click="passUser(user)"
                  class="w-14 h-14 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors"
                >
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <button 
                  @click="superlikeUser(user)"
                  class="w-12 h-12 bg-blue-100 hover:bg-blue-200 rounded-full flex items-center justify-center transition-colors"
                >
                  <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                </button>

                <button 
                  @click="likeUser(user)"
                  class="w-14 h-14 bg-pink-100 hover:bg-pink-200 rounded-full flex items-center justify-center transition-colors"
                >
                  <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action buttons for mobile -->
      <div v-if="currentUser" class="flex justify-center space-x-4">
        <button @click="passUser(currentUser)" class="btn-secondary flex-1">
          Přeskočit
        </button>
        <button @click="likeUser(currentUser)" class="btn-primary flex-1">
          Lajknout ❤️
        </button>
      </div>
    </div>

    <!-- Match Modal -->
    <div v-if="showMatchModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl p-6 max-w-sm w-full text-center">
        <div class="text-6xl mb-4">🎉</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Je to shoda!</h2>
        <p class="text-gray-600 mb-6">Vy a {{ matchedUser?.username }} jste si vzájemně lajkli</p>
        
        <div class="flex space-x-4">
          <button @click="closeMatchModal" class="btn-secondary flex-1">
            Pokračovat
          </button>
          <router-link :to="`/chat/${matchedUser?.matchId}`" class="btn-primary flex-1">
            Napsat zprávu
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import api from '@/services/api'
import SwipeCard from '@/components/SwipeCard.vue'

export default {
  name: 'Discover',
  components: {
    SwipeCard
  },
  setup() {
    const router = useRouter()
    const toast = useToast()

    const loading = ref(false)
    const showFilters = ref(false)
    const showMatchModal = ref(false)
    const isAnimating = ref(false)
    
    const users = ref([])
    const currentUserIndex = ref(0)
    const matchedUser = ref(null)

    const filters = ref({
      ageMin: 18,
      ageMax: 35,
      distance: 25,
      gender: ''
    })

    const currentUser = computed(() => users.value[currentUserIndex.value])
    const visibleUsers = computed(() => users.value.slice(currentUserIndex.value, currentUserIndex.value + 3))

    const loadUsers = async () => {
      try {
        loading.value = true
        const response = await api.get('/users/discover', {
          params: {
            limit: 10,
            offset: 0
          }
        })
        users.value = response.data.users
        currentUserIndex.value = 0
      } catch (error) {
        toast.error('Chyba při načítání uživatelů')
      } finally {
        loading.value = false
      }
    }

    const nextUser = () => {
      if (currentUserIndex.value < users.value.length - 1) {
        currentUserIndex.value++
      } else {
        loadUsers()
      }
    }

    const likeUser = async (user) => {
      try {
        isAnimating.value = true
        const response = await api.post('/matching/like', {
          liked_user_id: user.id
        })

        if (response.data.is_match) {
          matchedUser.value = { ...user, matchId: response.data.match.id }
          showMatchModal.value = true
        } else {
          toast.success('Lajk odeslán! 💕')
        }

        setTimeout(() => {
          nextUser()
          isAnimating.value = false
        }, 500)
      } catch (error) {
        toast.error('Chyba při lajkování')
        isAnimating.value = false
      }
    }

    const superlikeUser = async (user) => {
      try {
        isAnimating.value = true
        const response = await api.post('/matching/superlike', {
          liked_user_id: user.id
        })

        if (response.data.is_match) {
          matchedUser.value = { ...user, matchId: response.data.match.id }
          showMatchModal.value = true
        } else {
          toast.success('Superlajk odeslán! ⭐')
        }

        setTimeout(() => {
          nextUser()
          isAnimating.value = false
        }, 500)
      } catch (error) {
        toast.error('Chyba při superlajkování')
        isAnimating.value = false
      }
    }

    const passUser = async (user) => {
      try {
        await api.post('/matching/pass', {
          liked_user_id: user.id
        })
        nextUser()
      } catch (error) {
        toast.error('Chyba při přeskakování')
      }
    }

    const applyFilters = () => {
      showFilters.value = false
      loadUsers()
    }

    const closeMatchModal = () => {
      showMatchModal.value = false
      matchedUser.value = null
    }

    onMounted(() => {
      loadUsers()
    })

    return {
      loading,
      showFilters,
      showMatchModal,
      isAnimating,
      users,
      currentUser,
      visibleUsers,
      matchedUser,
      filters,
      loadUsers,
      likeUser,
      superlikeUser,
      passUser,
      applyFilters,
      closeMatchModal
    }
  }
}
</script>
