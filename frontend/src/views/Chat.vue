<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Conversations Sidebar -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
      <!-- Header -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <router-link to="/dashboard" class="text-xl font-bold gradient-text">💘 Linda</router-link>
          <h2 class="text-lg font-semibold text-gray-900">Zprávy</h2>
        </div>
      </div>

      <!-- Conversations List -->
      <div class="flex-1 overflow-y-auto">
        <div v-if="loadingConversations" class="p-4 text-center">
          <div class="loading-spinner mx-auto"></div>
        </div>

        <div v-else-if="conversations.length === 0" class="p-4 text-center text-gray-500">
          <div class="text-4xl mb-2">💬</div>
          <p><PERSON>at<PERSON><PERSON> žádn<PERSON> konverzace</p>
          <router-link to="/discover" class="text-pink-600 hover:text-pink-500 text-sm">
            Z<PERSON><PERSON><PERSON><PERSON> objevovat
          </router-link>
        </div>

        <div v-else>
          <div 
            v-for="conversation in conversations" 
            :key="conversation.id"
            @click="selectConversation(conversation)"
            class="p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{ 'bg-pink-50 border-pink-200': selectedConversation?.id === conversation.id }"
          >
            <div class="flex items-center space-x-3">
              <!-- Avatar -->
              <div class="relative">
                <img 
                  :src="getOtherUser(conversation).photos?.[0]?.url || '/default-avatar.png'" 
                  :alt="getOtherUser(conversation).username"
                  class="w-12 h-12 rounded-full object-cover"
                />
                <div 
                  v-if="isUserOnline(getOtherUser(conversation).id)"
                  class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"
                ></div>
              </div>

              <!-- Conversation Info -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h3 class="font-medium text-gray-900 truncate">
                    {{ getOtherUser(conversation).username }}
                  </h3>
                  <span v-if="conversation.messages?.[0]" class="text-xs text-gray-500">
                    {{ formatTime(conversation.messages[0].timestamp) }}
                  </span>
                </div>
                
                <p v-if="conversation.messages?.[0]" class="text-sm text-gray-600 truncate">
                  {{ conversation.messages[0].content }}
                </p>
                <p v-else class="text-sm text-gray-400 italic">
                  Začněte konverzaci...
                </p>

                <!-- Unread indicator -->
                <div v-if="hasUnreadMessages(conversation)" class="w-2 h-2 bg-pink-500 rounded-full mt-1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="flex-1 flex flex-col">
      <!-- No conversation selected -->
      <div v-if="!selectedConversation" class="flex-1 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <div class="text-6xl mb-4">💬</div>
          <h3 class="text-xl font-semibold mb-2">Vyberte konverzaci</h3>
          <p>Začněte chatovat s vašimi shodami</p>
        </div>
      </div>

      <!-- Active conversation -->
      <div v-else class="flex-1 flex flex-col">
        <!-- Chat Header -->
        <div class="bg-white border-b border-gray-200 p-4">
          <div class="flex items-center space-x-3">
            <img 
              :src="getOtherUser(selectedConversation).photos?.[0]?.url || '/default-avatar.png'" 
              :alt="getOtherUser(selectedConversation).username"
              class="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <h3 class="font-semibold text-gray-900">
                {{ getOtherUser(selectedConversation).username }}
              </h3>
              <p class="text-sm text-gray-500">
                {{ isUserOnline(getOtherUser(selectedConversation).id) ? 'Online' : 'Offline' }}
              </p>
            </div>
          </div>
        </div>

        <!-- Messages -->
        <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-4">
          <div v-if="loadingMessages" class="text-center">
            <div class="loading-spinner mx-auto"></div>
          </div>

          <div v-else-if="messages.length === 0" class="text-center text-gray-500">
            <div class="text-4xl mb-2">👋</div>
            <p>Začněte konverzaci s {{ getOtherUser(selectedConversation).username }}</p>
          </div>

          <div v-else>
            <div 
              v-for="message in messages" 
              :key="message.id"
              class="flex"
              :class="message.sender_id === currentUserId ? 'justify-end' : 'justify-start'"
            >
              <div 
                class="chat-bubble max-w-xs lg:max-w-md"
                :class="message.sender_id === currentUserId ? 'sent' : 'received'"
              >
                <p>{{ message.content }}</p>
                <div class="text-xs opacity-75 mt-1">
                  {{ formatTime(message.timestamp) }}
                  <span v-if="message.sender_id === currentUserId && message.read" class="ml-1">✓✓</span>
                  <span v-else-if="message.sender_id === currentUserId" class="ml-1">✓</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Typing indicator -->
        <div v-if="isTyping" class="px-4 py-2 text-sm text-gray-500">
          {{ getOtherUser(selectedConversation).username }} píše...
        </div>

        <!-- Message Input -->
        <div class="bg-white border-t border-gray-200 p-4">
          <form @submit.prevent="sendMessage" class="flex space-x-2">
            <input 
              v-model="newMessage"
              @input="handleTyping"
              type="text" 
              placeholder="Napište zprávu..."
              class="flex-1 input-field"
              :disabled="sending"
            />
            <button 
              type="submit" 
              :disabled="!newMessage.trim() || sending"
              class="btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="sending" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

export default {
  name: 'Chat',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    const toast = useToast()

    const loadingConversations = ref(false)
    const loadingMessages = ref(false)
    const sending = ref(false)
    const isTyping = ref(false)
    
    const conversations = ref([])
    const selectedConversation = ref(null)
    const messages = ref([])
    const newMessage = ref('')
    const messagesContainer = ref(null)
    const onlineUsers = ref(new Set())

    const currentUserId = computed(() => authStore.user?.id)

    const loadConversations = async () => {
      try {
        loadingConversations.value = true
        const response = await api.get('/chat/conversations')
        conversations.value = response.data.conversations
        
        // Auto-select conversation if matchId is provided
        if (route.params.matchId) {
          const conversation = conversations.value.find(c => c.id === parseInt(route.params.matchId))
          if (conversation) {
            selectConversation(conversation)
          }
        }
      } catch (error) {
        toast.error('Chyba při načítání konverzací')
      } finally {
        loadingConversations.value = false
      }
    }

    const loadMessages = async (conversationId) => {
      try {
        loadingMessages.value = true
        const response = await api.get(`/chat/conversations/${conversationId}/messages`)
        messages.value = response.data.messages.reverse() // Reverse to show oldest first
        
        // Scroll to bottom
        await nextTick()
        scrollToBottom()
      } catch (error) {
        toast.error('Chyba při načítání zpráv')
      } finally {
        loadingMessages.value = false
      }
    }

    const selectConversation = (conversation) => {
      selectedConversation.value = conversation
      loadMessages(conversation.id)
      
      // Update URL
      if (route.params.matchId !== conversation.id.toString()) {
        router.replace(`/chat/${conversation.id}`)
      }
    }

    const sendMessage = async () => {
      if (!newMessage.value.trim() || !selectedConversation.value) return

      try {
        sending.value = true
        const response = await api.post(`/chat/conversations/${selectedConversation.value.id}/messages`, {
          content: newMessage.value.trim()
        })

        messages.value.push(response.data)
        newMessage.value = ''
        
        // Update conversation list
        const conversation = conversations.value.find(c => c.id === selectedConversation.value.id)
        if (conversation) {
          conversation.messages = [response.data]
        }

        await nextTick()
        scrollToBottom()
      } catch (error) {
        toast.error('Chyba při odesílání zprávy')
      } finally {
        sending.value = false
      }
    }

    const handleTyping = () => {
      // In a real app, you would send typing indicators via WebSocket
      // For now, just a placeholder
    }

    const getOtherUser = (conversation) => {
      return conversation.user_a_id === currentUserId.value ? conversation.user_b : conversation.user_a
    }

    const isUserOnline = (userId) => {
      return onlineUsers.value.has(userId)
    }

    const hasUnreadMessages = (conversation) => {
      // Simplified unread logic
      return conversation.messages?.[0]?.sender_id !== currentUserId.value && !conversation.messages?.[0]?.read
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffInMinutes = (now - date) / (1000 * 60)

      if (diffInMinutes < 1) {
        return 'právě teď'
      } else if (diffInMinutes < 60) {
        return `před ${Math.floor(diffInMinutes)} min`
      } else if (diffInMinutes < 1440) {
        return `před ${Math.floor(diffInMinutes / 60)} h`
      } else {
        return date.toLocaleDateString('cs-CZ')
      }
    }

    const scrollToBottom = () => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }

    // Watch for new messages and scroll to bottom
    watch(messages, () => {
      nextTick(() => scrollToBottom())
    })

    onMounted(() => {
      loadConversations()
      
      // Simulate some online users
      setTimeout(() => {
        onlineUsers.value.add(1)
        onlineUsers.value.add(2)
        onlineUsers.value.add(4)
      }, 1000)
    })

    return {
      loadingConversations,
      loadingMessages,
      sending,
      isTyping,
      conversations,
      selectedConversation,
      messages,
      newMessage,
      messagesContainer,
      currentUserId,
      selectConversation,
      sendMessage,
      handleTyping,
      getOtherUser,
      isUserOnline,
      hasUnreadMessages,
      formatTime
    }
  }
}
</script>
