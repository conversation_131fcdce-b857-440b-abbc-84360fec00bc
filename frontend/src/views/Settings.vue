<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <router-link to="/dashboard" class="text-2xl font-bold gradient-text">💘 Linda</router-link>
          <h1 class="text-xl font-semibold text-gray-900">Nastavení</h1>
          <div class="w-20"></div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-2xl mx-auto py-6 px-4">
      <!-- Account Settings -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Účet</h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">E-mail</label>
            <input 
              v-model="settings.email" 
              type="email" 
              class="input-field"
              disabled
            />
            <p class="text-sm text-gray-500 mt-1">Pro změnu e-mailu kontaktujte podporu</p>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Uživatelské jméno</label>
            <input 
              v-model="settings.username" 
              type="text" 
              class="input-field"
            />
          </div>

          <div>
            <button @click="showChangePassword = true" class="btn-outline">
              Změnit heslo
            </button>
          </div>
        </div>
      </div>

      <!-- Privacy Settings -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Soukromí</h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Zobrazit můj věk</h3>
              <p class="text-sm text-gray-600">Ostatní uživatelé uvidí váš věk</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.showAge" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Zobrazit vzdálenost</h3>
              <p class="text-sm text-gray-600">Ostatní uživatelé uvidí vaši vzdálenost</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.showDistance" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Online status</h3>
              <p class="text-sm text-gray-600">Zobrazit kdy jste naposledy aktivní</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.showOnlineStatus" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Notifikace</h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Nové zprávy</h3>
              <p class="text-sm text-gray-600">Upozornění na nové zprávy</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifyMessages" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Nové shody</h3>
              <p class="text-sm text-gray-600">Upozornění na nové shody</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifyMatches" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Lajky</h3>
              <p class="text-sm text-gray-600">Upozornění na nové lajky</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifyLikes" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Marketingové e-maily</h3>
              <p class="text-sm text-gray-600">Tipy a novinky o aplikaci</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifyMarketing" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- Discovery Settings -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Objevování</h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Maximální vzdálenost (km)</label>
            <input 
              v-model="settings.maxDistance" 
              type="range" 
              min="1" 
              max="100" 
              class="w-full"
            />
            <div class="text-center text-sm text-gray-600">{{ settings.maxDistance }} km</div>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-medium text-gray-900">Globální režim</h3>
              <p class="text-sm text-gray-600">Objevovat lidi po celém světě</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.globalMode" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-pink-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-pink-600"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- Data & Privacy -->
      <div class="card mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Data a soukromí</h2>
        
        <div class="space-y-4">
          <button class="btn-outline w-full">
            Stáhnout moje data
          </button>
          
          <button class="btn-outline w-full">
            Vymazat historii zpráv
          </button>
          
          <button @click="showDeleteAccount = true" class="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg">
            Smazat účet
          </button>
        </div>
      </div>

      <!-- Save Button -->
      <div class="text-center">
        <button @click="saveSettings" :disabled="saving" class="btn-primary px-8">
          {{ saving ? 'Ukládám...' : 'Uložit nastavení' }}
        </button>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div v-if="showChangePassword" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl p-6 max-w-md w-full">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Změnit heslo</h2>
        
        <form @submit.prevent="changePassword" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Současné heslo</label>
            <input 
              v-model="passwordForm.current" 
              type="password" 
              required
              class="input-field"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Nové heslo</label>
            <input 
              v-model="passwordForm.new" 
              type="password" 
              required
              minlength="6"
              class="input-field"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Potvrdit nové heslo</label>
            <input 
              v-model="passwordForm.confirm" 
              type="password" 
              required
              class="input-field"
            />
          </div>

          <div class="flex space-x-4">
            <button type="button" @click="showChangePassword = false" class="btn-secondary flex-1">
              Zrušit
            </button>
            <button type="submit" :disabled="changingPassword" class="btn-primary flex-1">
              {{ changingPassword ? 'Měním...' : 'Změnit' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Account Modal -->
    <div v-if="showDeleteAccount" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl p-6 max-w-md w-full text-center">
        <div class="text-4xl mb-4">⚠️</div>
        <h2 class="text-xl font-bold text-gray-900 mb-2">Smazat účet?</h2>
        <p class="text-gray-600 mb-6">
          Tato akce je nevratná. Všechna vaše data budou trvale smazána.
        </p>
        
        <div class="space-y-4">
          <input 
            v-model="deleteConfirmation" 
            type="text" 
            placeholder="Napište 'SMAZAT' pro potvrzení"
            class="input-field"
          />
          
          <div class="flex space-x-4">
            <button @click="showDeleteAccount = false" class="btn-secondary flex-1">
              Zrušit
            </button>
            <button 
              @click="deleteAccount" 
              :disabled="deleteConfirmation !== 'SMAZAT' || deleting"
              class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ deleting ? 'Mažu...' : 'Smazat účet' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

export default {
  name: 'Settings',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const toast = useToast()

    const saving = ref(false)
    const changingPassword = ref(false)
    const deleting = ref(false)
    const showChangePassword = ref(false)
    const showDeleteAccount = ref(false)
    const deleteConfirmation = ref('')

    const settings = ref({
      email: '',
      username: '',
      showAge: true,
      showDistance: true,
      showOnlineStatus: true,
      notifyMessages: true,
      notifyMatches: true,
      notifyLikes: true,
      notifyMarketing: false,
      maxDistance: 25,
      globalMode: false
    })

    const passwordForm = ref({
      current: '',
      new: '',
      confirm: ''
    })

    const loadSettings = () => {
      const user = authStore.user
      if (user) {
        settings.value.email = user.email
        settings.value.username = user.username
      }
    }

    const saveSettings = async () => {
      try {
        saving.value = true
        // In a real app, you would save settings to the backend
        await new Promise(resolve => setTimeout(resolve, 1000))
        toast.success('Nastavení byla uložena')
      } catch (error) {
        toast.error('Chyba při ukládání nastavení')
      } finally {
        saving.value = false
      }
    }

    const changePassword = async () => {
      if (passwordForm.value.new !== passwordForm.value.confirm) {
        toast.error('Hesla se neshodují')
        return
      }

      try {
        changingPassword.value = true
        // In a real app, you would change password via API
        await new Promise(resolve => setTimeout(resolve, 1000))
        toast.success('Heslo bylo změněno')
        showChangePassword.value = false
        passwordForm.value = { current: '', new: '', confirm: '' }
      } catch (error) {
        toast.error('Chyba při změně hesla')
      } finally {
        changingPassword.value = false
      }
    }

    const deleteAccount = async () => {
      try {
        deleting.value = true
        // In a real app, you would delete account via API
        await new Promise(resolve => setTimeout(resolve, 2000))
        toast.success('Účet byl smazán')
        await authStore.logout()
        router.push('/')
      } catch (error) {
        toast.error('Chyba při mazání účtu')
      } finally {
        deleting.value = false
      }
    }

    onMounted(() => {
      loadSettings()
    })

    return {
      saving,
      changingPassword,
      deleting,
      showChangePassword,
      showDeleteAccount,
      deleteConfirmation,
      settings,
      passwordForm,
      saveSettings,
      changePassword,
      deleteAccount
    }
  }
}
</script>
