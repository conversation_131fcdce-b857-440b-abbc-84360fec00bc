<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
    <!-- Navigation -->
    <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-2xl font-bold gradient-text">💘 Linda</h1>
          </div>
          <div class="flex items-center space-x-4">
            <router-link to="/login" class="btn-outline">
              Přihlásit se
            </router-link>
            <router-link to="/register" class="btn-primary">
              Registrovat se
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden py-20 lg:py-32">
      <!-- Animated background -->
      <div class="absolute inset-0 bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ec4899" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse"></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <!-- Animated title -->
          <div class="animate-fade-in-up">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Najděte svou
              <span class="gradient-text relative">
                pravou lásku
                <div class="absolute -inset-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg blur opacity-25 animate-pulse"></div>
              </span>
            </h1>
          </div>

          <!-- Animated subtitle -->
          <div class="animate-fade-in-up animation-delay-200">
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Moderní seznamovací aplikace pro vážné vztahy i neformální setkání.
              Bezpečné, inteligentní a navržené pro skutečné spojení.
            </p>
          </div>

          <!-- Animated buttons -->
          <div class="animate-fade-in-up animation-delay-400">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <router-link
                to="/register"
                class="btn-primary text-lg px-8 py-4 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <span class="flex items-center justify-center">
                  Začít hned
                  <span class="ml-2 animate-bounce">💕</span>
                </span>
              </router-link>
              <button
                @click="scrollToFeatures"
                class="btn-secondary text-lg px-8 py-4 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Zjistit více
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced floating hearts animation -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="heart-float heart-float-1" style="left: 10%; animation-delay: 0s;">💕</div>
        <div class="heart-float heart-float-2" style="left: 20%; animation-delay: 2s;">💖</div>
        <div class="heart-float heart-float-3" style="left: 30%; animation-delay: 4s;">💗</div>
        <div class="heart-float heart-float-4" style="left: 70%; animation-delay: 1s;">💘</div>
        <div class="heart-float heart-float-5" style="left: 80%; animation-delay: 3s;">💝</div>
        <div class="heart-float heart-float-6" style="left: 90%; animation-delay: 5s;">💞</div>
      </div>

      <!-- Floating geometric shapes -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-4 h-4 bg-pink-300 rounded-full animate-float opacity-60"></div>
        <div class="absolute top-3/4 right-1/4 w-6 h-6 bg-purple-300 rounded-full animate-float-delayed opacity-60"></div>
        <div class="absolute top-1/2 left-3/4 w-3 h-3 bg-indigo-300 rounded-full animate-float-slow opacity-60"></div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features-section" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Proč zvolit Lindu?
          </h2>
          <p class="text-xl text-gray-600">
            Funkce navržené pro skutečné spojení
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🔒</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Bezpečnost první</h3>
            <p class="text-gray-600">
              Ověřené profily, moderace obsahu a pokročilé zabezpečení
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>

          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🤖</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">AI doporučení</h3>
            <p class="text-gray-600">
              Inteligentní algoritmus pro nalezení kompatibilních partnerů
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>

          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">💬</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Realtime chat</h3>
            <p class="text-gray-600">
              Okamžité zprávy s indikátory psaní a čtení
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>

          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">📍</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Lokální shody</h3>
            <p class="text-gray-600">
              Najděte lidi ve vašem okolí s respektem k soukromí
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>

          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">⭐</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Superlike</h3>
            <p class="text-gray-600">
              Ukažte zvýšený zájem a získejte pozornost
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>

          <div class="card text-center hover-lift group cursor-pointer">
            <div class="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">🎯</div>
            <h3 class="text-xl font-semibold mb-2 group-hover:text-pink-600 transition-colors">Přesné filtry</h3>
            <p class="text-gray-600">
              Nastavte si preference podle věku, zájmů a vzdálenosti
            </p>
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-pink-500 to-purple-600">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
          Připraveni najít lásku?
        </h2>
        <p class="text-xl text-pink-100 mb-8">
          Připojte se k tisícům lidí, kteří už našli své štěstí
        </p>
        <router-link to="/register" class="bg-white text-pink-600 hover:bg-gray-100 font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200">
          Začít zdarma
        </router-link>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-xl font-bold mb-4">💘 Linda</h3>
            <p class="text-gray-400">
              Moderní seznamovací aplikace pro skutečné spojení
            </p>
          </div>
          <div>
            <h4 class="font-semibold mb-4">Produkt</h4>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white">Funkce</a></li>
              <li><a href="#" class="hover:text-white">Bezpečnost</a></li>
              <li><a href="#" class="hover:text-white">Prémiové členství</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold mb-4">Podpora</h4>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white">Nápověda</a></li>
              <li><a href="#" class="hover:text-white">Kontakt</a></li>
              <li><a href="#" class="hover:text-white">FAQ</a></li>
            </ul>
          </div>
          <div>
            <h4 class="font-semibold mb-4">Právní</h4>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white">Podmínky použití</a></li>
              <li><a href="#" class="hover:text-white">Ochrana soukromí</a></li>
              <li><a href="#" class="hover:text-white">Cookies</a></li>
            </ul>
          </div>
        </div>
        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Linda Dating App. Všechna práva vyhrazena.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'Home',
  methods: {
    scrollToFeatures() {
      const featuresSection = document.querySelector('#features-section')
      if (featuresSection) {
        featuresSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }
}
</script>

<style scoped>
.heart-float {
  position: absolute;
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
}

@keyframes float {
  0%, 100% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10%, 90% {
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10vh) rotate(180deg);
    opacity: 1;
  }
}
</style>
