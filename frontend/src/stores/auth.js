import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const loading = ref(false)
  const error = ref(null)

  const isAuthenticated = computed(() => !!token.value && !!user.value)

  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
      api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
    } else {
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
    }
  }

  const setUser = (userData) => {
    user.value = userData
  }

  const login = async (email, password) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await api.post('/auth/login', {
        email,
        password
      })
      
      const { user: userData, token: userToken } = response.data
      
      setToken(userToken)
      setUser(userData)
      
      return { success: true }
    } catch (err) {
      error.value = err.response?.data?.error || 'Přihlášení se nezdařilo'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const register = async (username, email, password) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await api.post('/auth/register', {
        username,
        email,
        password
      })
      
      const { user: userData, token: userToken } = response.data
      
      setToken(userToken)
      setUser(userData)
      
      return { success: true }
    } catch (err) {
      error.value = err.response?.data?.error || 'Registrace se nezdařila'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      await api.post('/auth/logout')
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      setToken(null)
      setUser(null)
    }
  }

  const refreshToken = async () => {
    try {
      if (!token.value) return false
      
      const response = await api.post('/auth/refresh', {
        token: token.value
      })
      
      setToken(response.data.token)
      return true
    } catch (err) {
      console.error('Token refresh failed:', err)
      logout()
      return false
    }
  }

  const fetchUser = async () => {
    try {
      if (!token.value) return false
      
      const response = await api.get('/users/profile')
      setUser(response.data)
      return true
    } catch (err) {
      console.error('Fetch user failed:', err)
      logout()
      return false
    }
  }

  // Initialize auth state
  const init = async () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      await fetchUser()
    }
  }

  return {
    user,
    token,
    loading,
    error,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    fetchUser,
    init
  }
})
