package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"linda-dating-app/internal/config"
	"linda-dating-app/internal/database"
	"linda-dating-app/internal/handler"
	"linda-dating-app/internal/middleware"
	"linda-dating-app/internal/repository"
	"linda-dating-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Run migrations
	if err := database.Migrate(db); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// Initialize Redis
	rdb := redis.NewClient(&redis.Options{
		Addr: cfg.RedisURL,
	})

	// Test Redis connection
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatal("Failed to connect to Redis:", err)
	}

	// Initialize repositories
	userRepo := repository.NewUserRepository(db)
	photoRepo := repository.NewPhotoRepository(db)
	likeRepo := repository.NewLikeRepository(db)
	matchRepo := repository.NewMatchRepository(db)
	messageRepo := repository.NewMessageRepository(db)
	reportRepo := repository.NewReportRepository(db)
	notificationRepo := repository.NewNotificationRepository(db)

	// Initialize services
	authService := service.NewAuthService(userRepo, cfg.JWTSecret)
	userService := service.NewUserService(userRepo, photoRepo)
	matchingService := service.NewMatchingService(userRepo, likeRepo, matchRepo)
	chatService := service.NewChatService(messageRepo, matchRepo)
	moderationService := service.NewModerationService(reportRepo, userRepo)
	notificationService := service.NewNotificationService(notificationRepo, rdb)

	// Initialize handlers
	authHandler := handler.NewAuthHandler(authService)
	userHandler := handler.NewUserHandler(userService, authService)
	matchingHandler := handler.NewMatchingHandler(matchingService, authService)
	chatHandler := handler.NewChatHandler(chatService, authService)
	moderationHandler := handler.NewModerationHandler(moderationService, authService)
	notificationHandler := handler.NewNotificationHandler(notificationService, authService)

	// Setup Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RateLimit(rdb))

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"version":   "2.0.0",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", middleware.AuthRequired(authService), authHandler.Logout)
		}

		// User routes
		users := v1.Group("/users")
		users.Use(middleware.AuthRequired(authService))
		{
			users.GET("/profile", userHandler.GetProfile)
			users.PUT("/profile", userHandler.UpdateProfile)
			users.POST("/photos", userHandler.UploadPhoto)
			users.DELETE("/photos/:id", userHandler.DeletePhoto)
			users.PUT("/photos/:id/primary", userHandler.SetPrimaryPhoto)
			users.GET("/discover", userHandler.DiscoverUsers)
		}

		// Matching routes
		matching := v1.Group("/matching")
		matching.Use(middleware.AuthRequired(authService))
		{
			matching.POST("/like", matchingHandler.LikeUser)
			matching.POST("/superlike", matchingHandler.SuperlikeUser)
			matching.POST("/pass", matchingHandler.PassUser)
			matching.GET("/matches", matchingHandler.GetMatches)
			matching.DELETE("/matches/:id", matchingHandler.UnmatchUser)
		}

		// Chat routes
		chat := v1.Group("/chat")
		chat.Use(middleware.AuthRequired(authService))
		{
			chat.GET("/conversations", chatHandler.GetConversations)
			chat.GET("/conversations/:id/messages", chatHandler.GetMessages)
			chat.POST("/conversations/:id/messages", chatHandler.SendMessage)
			chat.PUT("/messages/:id/read", chatHandler.MarkAsRead)
			chat.GET("/ws", chatHandler.HandleWebSocket)
		}

		// Moderation routes
		moderation := v1.Group("/moderation")
		moderation.Use(middleware.AuthRequired(authService))
		{
			moderation.POST("/reports", moderationHandler.CreateReport)
			moderation.GET("/reports", middleware.AdminRequired(), moderationHandler.GetReports)
			moderation.PUT("/reports/:id", middleware.AdminRequired(), moderationHandler.UpdateReport)
		}

		// Notification routes
		notifications := v1.Group("/notifications")
		notifications.Use(middleware.AuthRequired(authService))
		{
			notifications.GET("/", notificationHandler.GetNotifications)
			notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
			notifications.DELETE("/:id", notificationHandler.DeleteNotification)
		}
	}

	// Start server
	srv := &http.Server{
		Addr:    ":" + cfg.Port,
		Handler: router,
	}

	// Graceful shutdown
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	log.Printf("Server started on port %s", cfg.Port)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}
