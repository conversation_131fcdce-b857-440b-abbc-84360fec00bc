package database

import (
	"linda-dating-app/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Connect(databaseURL string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	return db, nil
}

func Migrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.User{},
		&models.Photo{},
		&models.UserInterest{},
		&models.Like{},
		&models.Match{},
		&models.Message{},
		&models.Report{},
		&models.Notification{},
		&models.AdminLog{},
	)
}
