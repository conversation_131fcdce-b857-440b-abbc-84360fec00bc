package repository

import (
	"linda-dating-app/internal/models"

	"gorm.io/gorm"
)

type MessageRepository struct {
	db *gorm.DB
}

func NewMessageRepository(db *gorm.DB) *MessageRepository {
	return &MessageRepository{db: db}
}

func (r *MessageRepository) Create(message *models.Message) error {
	return r.db.Create(message).Error
}

func (r *MessageRepository) GetByID(id uint) (*models.Message, error) {
	var message models.Message
	err := r.db.Preload("Sender").Preload("Receiver").First(&message, id).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

func (r *MessageRepository) GetByMatchID(matchID uint, limit, offset int) ([]models.Message, error) {
	var messages []models.Message
	err := r.db.Preload("Sender").Preload("Receiver").
		Where("match_id = ?", matchID).
		Order("timestamp DESC").
		Limit(limit).
		Offset(offset).
		Find(&messages).Error
	return messages, err
}

func (r *MessageRepository) MarkAsRead(id uint) error {
	return r.db.Model(&models.Message{}).Where("id = ?", id).Update("read", true).Error
}

func (r *MessageRepository) GetUnreadCount(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&models.Message{}).
		Where("receiver_id = ? AND read = false", userID).
		Count(&count).Error
	return count, err
}

func (r *MessageRepository) GetConversations(userID uint) ([]models.Match, error) {
	var matches []models.Match
	err := r.db.Preload("UserA").Preload("UserB").
		Preload("Messages", func(db *gorm.DB) *gorm.DB {
			return db.Order("timestamp DESC").Limit(1)
		}).
		Where("(user_a_id = ? OR user_b_id = ?) AND chat_enabled = true", userID, userID).
		Find(&matches).Error
	return matches, err
}
