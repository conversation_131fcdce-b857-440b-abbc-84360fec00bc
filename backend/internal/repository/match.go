package repository

import (
	"linda-dating-app/internal/models"

	"gorm.io/gorm"
)

type MatchRepository struct {
	db *gorm.DB
}

func NewMatchRepository(db *gorm.DB) *MatchRepository {
	return &MatchRepository{db: db}
}

func (r *MatchRepository) Create(match *models.Match) error {
	return r.db.Create(match).Error
}

func (r *MatchRepository) GetByID(id uint) (*models.Match, error) {
	var match models.Match
	err := r.db.Preload("UserA").Preload("UserB").First(&match, id).Error
	if err != nil {
		return nil, err
	}
	return &match, nil
}

func (r *MatchRepository) GetByUsers(userAID, userBID uint) (*models.Match, error) {
	var match models.Match
	err := r.db.Where("(user_a_id = ? AND user_b_id = ?) OR (user_a_id = ? AND user_b_id = ?)", 
		userAID, userBID, userBID, userAID).First(&match).Error
	if err != nil {
		return nil, err
	}
	return &match, nil
}

func (r *MatchRepository) GetUserMatches(userID uint) ([]models.Match, error) {
	var matches []models.Match
	err := r.db.Preload("UserA").Preload("UserB").
		Where("user_a_id = ? OR user_b_id = ?", userID, userID).
		Find(&matches).Error
	return matches, err
}

func (r *MatchRepository) Delete(id uint) error {
	return r.db.Delete(&models.Match{}, id).Error
}

func (r *MatchRepository) UpdateChatEnabled(id uint, enabled bool) error {
	return r.db.Model(&models.Match{}).Where("id = ?", id).Update("chat_enabled", enabled).Error
}
