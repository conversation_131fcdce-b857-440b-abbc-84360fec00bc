package repository

import (
	"linda-dating-app/internal/models"

	"gorm.io/gorm"
)

type PhotoRepository struct {
	db *gorm.DB
}

func NewPhotoRepository(db *gorm.DB) *PhotoRepository {
	return &PhotoRepository{db: db}
}

func (r *PhotoRepository) Create(photo *models.Photo) error {
	return r.db.Create(photo).Error
}

func (r *PhotoRepository) GetByID(id uint) (*models.Photo, error) {
	var photo models.Photo
	err := r.db.First(&photo, id).Error
	if err != nil {
		return nil, err
	}
	return &photo, nil
}

func (r *PhotoRepository) GetByUserID(userID uint) ([]models.Photo, error) {
	var photos []models.Photo
	err := r.db.Where("user_id = ?", userID).Order("\"order\" ASC").Find(&photos).Error
	return photos, err
}

func (r *PhotoRepository) Update(photo *models.Photo) error {
	return r.db.Save(photo).Error
}

func (r *PhotoRepository) Delete(id uint) error {
	return r.db.Delete(&models.Photo{}, id).Error
}

func (r *PhotoRepository) SetPrimary(userID uint, photoID uint) error {
	// First, unset all primary photos for the user
	err := r.db.Model(&models.Photo{}).Where("user_id = ?", userID).Update("is_primary", false).Error
	if err != nil {
		return err
	}
	
	// Then set the specified photo as primary
	return r.db.Model(&models.Photo{}).Where("id = ? AND user_id = ?", photoID, userID).Update("is_primary", true).Error
}
