package repository

import (
	"linda-dating-app/internal/models"

	"gorm.io/gorm"
)

type LikeRepository struct {
	db *gorm.DB
}

func NewLikeRepository(db *gorm.DB) *LikeRepository {
	return &LikeRepository{db: db}
}

func (r *LikeRepository) Create(like *models.Like) error {
	return r.db.Create(like).Error
}

func (r *LikeRepository) GetByUsers(likerID, likedID uint) (*models.Like, error) {
	var like models.Like
	err := r.db.Where("liker_id = ? AND liked_id = ?", likerID, likedID).First(&like).Error
	if err != nil {
		return nil, err
	}
	return &like, nil
}

func (r *LikeRepository) CheckMutualLike(userAID, userBID uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.Like{}).
		Where("(liker_id = ? AND liked_id = ?) OR (liker_id = ? AND liked_id = ?)", 
			userAID, userBID, userBID, userAID).
		Count(&count).Error
	
	return count == 2, err
}

func (r *LikeRepository) GetLikesReceived(userID uint) ([]models.Like, error) {
	var likes []models.Like
	err := r.db.Preload("Liker").Where("liked_id = ?", userID).Find(&likes).Error
	return likes, err
}

func (r *LikeRepository) GetLikesSent(userID uint) ([]models.Like, error) {
	var likes []models.Like
	err := r.db.Preload("Liked").Where("liker_id = ?", userID).Find(&likes).Error
	return likes, err
}
