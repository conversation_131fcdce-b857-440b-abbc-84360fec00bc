package repository

import (
	"linda-dating-app/internal/models"

	"gorm.io/gorm"
)

type ReportRepository struct {
	db *gorm.DB
}

func NewReportRepository(db *gorm.DB) *ReportRepository {
	return &ReportRepository{db: db}
}

func (r *ReportRepository) Create(report *models.Report) error {
	return r.db.Create(report).Error
}

func (r *ReportRepository) GetByID(id uint) (*models.Report, error) {
	var report models.Report
	err := r.db.Preload("Reporter").Preload("ReportedUser").First(&report, id).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

func (r *ReportRepository) GetAll(limit, offset int) ([]models.Report, error) {
	var reports []models.Report
	err := r.db.Preload("Reporter").Preload("ReportedUser").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&reports).Error
	return reports, err
}

func (r *ReportRepository) GetByStatus(status string, limit, offset int) ([]models.Report, error) {
	var reports []models.Report
	err := r.db.Preload("Reporter").Preload("ReportedUser").
		Where("status = ?", status).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&reports).Error
	return reports, err
}

func (r *ReportRepository) Update(report *models.Report) error {
	return r.db.Save(report).Error
}

func (r *ReportRepository) UpdateStatus(id uint, status string) error {
	return r.db.Model(&models.Report{}).Where("id = ?", id).Update("status", status).Error
}
