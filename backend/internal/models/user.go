package models

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID                   uint      `json:"id" gorm:"primaryKey"`
	Username             string    `json:"username" gorm:"uniqueIndex;not null"`
	Email                string    `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash         string    `json:"-" gorm:"not null"`
	Age                  int       `json:"age"`
	Gender               string    `json:"gender"`
	SexualOrientation    string    `json:"sexual_orientation"`
	Bio                  string    `json:"bio"`
	Location             string    `json:"location"` // Will be enhanced with PostGIS later
	LookingFor           string    `json:"looking_for"`
	PreferencesAgeMin    int       `json:"preferences_age_min"`
	PreferencesAgeMax    int       `json:"preferences_age_max"`
	PreferencesGender    string    `json:"preferences_gender"`
	IsVerified           bool      `json:"is_verified" gorm:"default:false"`
	LastActiveAt         time.Time `json:"last_active_at"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	DeletedAt            gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Photos        []Photo        `json:"photos,omitempty" gorm:"foreignKey:UserID"`
	Interests     []UserInterest `json:"interests,omitempty" gorm:"foreignKey:UserID"`
	SentLikes     []Like         `json:"-" gorm:"foreignKey:LikerID"`
	ReceivedLikes []Like         `json:"-" gorm:"foreignKey:LikedID"`
	Notifications []Notification `json:"-" gorm:"foreignKey:UserID"`
}

type Photo struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id" gorm:"not null"`
	URL        string    `json:"url" gorm:"not null"`
	IsPrimary  bool      `json:"is_primary" gorm:"default:false"`
	Order      int       `json:"order" gorm:"default:0"`
	IsNSFW     bool      `json:"is_nsfw" gorm:"default:false"`
	UploadedAt time.Time `json:"uploaded_at"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	// Relationships
	User User `json:"-" gorm:"foreignKey:UserID"`
}

type UserInterest struct {
	UserID      uint   `json:"user_id" gorm:"primaryKey"`
	InterestTag string `json:"interest_tag" gorm:"primaryKey"`

	// Relationships
	User User `json:"-" gorm:"foreignKey:UserID"`
}

type Like struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	LikerID   uint      `json:"liker_id" gorm:"not null"`
	LikedID   uint      `json:"liked_id" gorm:"not null"`
	Type      string    `json:"type" gorm:"not null;check:type IN ('like', 'superlike')"`
	CreatedAt time.Time `json:"created_at"`

	// Relationships
	Liker User `json:"liker,omitempty" gorm:"foreignKey:LikerID"`
	Liked User `json:"liked,omitempty" gorm:"foreignKey:LikedID"`
}

type Match struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserAID     uint      `json:"user_a_id" gorm:"not null"`
	UserBID     uint      `json:"user_b_id" gorm:"not null"`
	ChatEnabled bool      `json:"chat_enabled" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`

	// Relationships
	UserA    User      `json:"user_a,omitempty" gorm:"foreignKey:UserAID"`
	UserB    User      `json:"user_b,omitempty" gorm:"foreignKey:UserBID"`
	Messages []Message `json:"messages,omitempty" gorm:"foreignKey:MatchID"`
}

type Message struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	MatchID    uint      `json:"match_id" gorm:"not null"`
	SenderID   uint      `json:"sender_id" gorm:"not null"`
	ReceiverID uint      `json:"receiver_id" gorm:"not null"`
	Content    string    `json:"content" gorm:"not null"`
	Read       bool      `json:"read" gorm:"default:false"`
	Timestamp  time.Time `json:"timestamp"`
	CreatedAt  time.Time `json:"created_at"`

	// Relationships
	Match    Match `json:"-" gorm:"foreignKey:MatchID"`
	Sender   User  `json:"sender,omitempty" gorm:"foreignKey:SenderID"`
	Receiver User  `json:"receiver,omitempty" gorm:"foreignKey:ReceiverID"`
}

type Report struct {
	ID             uint       `json:"id" gorm:"primaryKey"`
	ReporterID     uint       `json:"reporter_id" gorm:"not null"`
	ReportedUserID uint       `json:"reported_user_id" gorm:"not null"`
	Reason         string     `json:"reason" gorm:"not null"`
	Type           string     `json:"type" gorm:"not null"`
	Status         string     `json:"status" gorm:"default:pending"`
	CreatedAt      time.Time  `json:"created_at"`
	ResolvedAt     *time.Time `json:"resolved_at,omitempty"`

	// Relationships
	Reporter     User `json:"reporter,omitempty" gorm:"foreignKey:ReporterID"`
	ReportedUser User `json:"reported_user,omitempty" gorm:"foreignKey:ReportedUserID"`
}

type Notification struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Message   string    `json:"message" gorm:"not null"`
	Type      string    `json:"type" gorm:"not null"`
	IsRead    bool      `json:"is_read" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`

	// Relationships
	User User `json:"-" gorm:"foreignKey:UserID"`
}

type AdminLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	AdminID    uint      `json:"admin_id" gorm:"not null"`
	Action     string    `json:"action" gorm:"not null"`
	EntityType string    `json:"entity_type" gorm:"not null"`
	EntityID   uint      `json:"entity_id" gorm:"not null"`
	Details    string    `json:"details" gorm:"type:jsonb"`
	Timestamp  time.Time `json:"timestamp"`

	// Relationships
	Admin User `json:"admin,omitempty" gorm:"foreignKey:AdminID"`
}
