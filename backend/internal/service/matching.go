package service

import (
	"errors"
	"time"

	"linda-dating-app/internal/models"
	"linda-dating-app/internal/repository"
)

type MatchingService struct {
	userRepo  *repository.UserRepository
	likeRepo  *repository.LikeRepository
	matchRepo *repository.MatchRepository
}

func NewMatchingService(userRepo *repository.UserRepository, likeRepo *repository.LikeRepository, matchRepo *repository.MatchRepository) *MatchingService {
	return &MatchingService{
		userRepo:  userRepo,
		likeRepo:  likeRepo,
		matchRepo: matchRepo,
	}
}

func (s *MatchingService) LikeUser(likerID, likedID uint) (*models.Match, error) {
	// Check if already liked
	if _, err := s.likeRepo.GetByUsers(likerID, likedID); err == nil {
		return nil, errors.New("user already liked")
	}

	// Create like
	like := &models.Like{
		LikerID:   likerID,
		LikedID:   likedID,
		Type:      "like",
		CreatedAt: time.Now(),
	}

	if err := s.likeRepo.Create(like); err != nil {
		return nil, err
	}

	// Check for mutual like
	isMutual, err := s.likeRepo.CheckMutualLike(likerID, likedID)
	if err != nil {
		return nil, err
	}

	if isMutual {
		// Create match
		match := &models.Match{
			UserAID:   likerID,
			UserBID:   likedID,
			CreatedAt: time.Now(),
		}

		if err := s.matchRepo.Create(match); err != nil {
			return nil, err
		}

		return match, nil
	}

	return nil, nil
}

func (s *MatchingService) SuperlikeUser(likerID, likedID uint) (*models.Match, error) {
	// Check if already liked
	if _, err := s.likeRepo.GetByUsers(likerID, likedID); err == nil {
		return nil, errors.New("user already liked")
	}

	// Create superlike
	like := &models.Like{
		LikerID:   likerID,
		LikedID:   likedID,
		Type:      "superlike",
		CreatedAt: time.Now(),
	}

	if err := s.likeRepo.Create(like); err != nil {
		return nil, err
	}

	// Check for mutual like
	isMutual, err := s.likeRepo.CheckMutualLike(likerID, likedID)
	if err != nil {
		return nil, err
	}

	if isMutual {
		// Create match
		match := &models.Match{
			UserAID:   likerID,
			UserBID:   likedID,
			CreatedAt: time.Now(),
		}

		if err := s.matchRepo.Create(match); err != nil {
			return nil, err
		}

		return match, nil
	}

	return nil, nil
}

func (s *MatchingService) PassUser(likerID, likedID uint) error {
	// For now, we don't store passes, just return success
	// In the future, we might want to store passes to avoid showing the same user again
	return nil
}

func (s *MatchingService) GetMatches(userID uint) ([]models.Match, error) {
	return s.matchRepo.GetUserMatches(userID)
}

func (s *MatchingService) UnmatchUser(userID, matchID uint) error {
	match, err := s.matchRepo.GetByID(matchID)
	if err != nil {
		return err
	}

	// Check if user is part of this match
	if match.UserAID != userID && match.UserBID != userID {
		return errors.New("unauthorized")
	}

	return s.matchRepo.Delete(matchID)
}
