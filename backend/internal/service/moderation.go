package service

import (
	"time"

	"linda-dating-app/internal/models"
	"linda-dating-app/internal/repository"
)

type ModerationService struct {
	reportRepo *repository.ReportRepository
	userRepo   *repository.UserRepository
}

func NewModerationService(reportRepo *repository.ReportRepository, userRepo *repository.UserRepository) *ModerationService {
	return &ModerationService{
		reportRepo: reportRepo,
		userRepo:   userRepo,
	}
}

func (s *ModerationService) CreateReport(reporterID, reportedUserID uint, reason, reportType string) (*models.Report, error) {
	report := &models.Report{
		ReporterID:     reporterID,
		ReportedUserID: reportedUserID,
		Reason:         reason,
		Type:           reportType,
		Status:         "pending",
		CreatedAt:      time.Now(),
	}

	if err := s.reportRepo.Create(report); err != nil {
		return nil, err
	}

	return report, nil
}

func (s *ModerationService) GetReports(status string, limit, offset int) ([]models.Report, error) {
	if status != "" {
		return s.reportRepo.GetByStatus(status, limit, offset)
	}
	return s.reportRepo.GetAll(limit, offset)
}

func (s *ModerationService) UpdateReport(reportID uint, status string) (*models.Report, error) {
	report, err := s.reportRepo.GetByID(reportID)
	if err != nil {
		return nil, err
	}

	report.Status = status
	if status == "resolved" {
		now := time.Now()
		report.ResolvedAt = &now
	}

	if err := s.reportRepo.Update(report); err != nil {
		return nil, err
	}

	return report, nil
}
