package service

import (
	"errors"

	"linda-dating-app/internal/models"
	"linda-dating-app/internal/repository"
)

type UserService struct {
	userRepo  *repository.UserRepository
	photoRepo *repository.PhotoRepository
}

func NewUserService(userRepo *repository.UserRepository, photoRepo *repository.PhotoRepository) *UserService {
	return &UserService{
		userRepo:  userRepo,
		photoRepo: photoRepo,
	}
}

func (s *UserService) GetProfile(userID uint) (*models.User, error) {
	return s.userRepo.GetByID(userID)
}

func (s *UserService) UpdateProfile(userID uint, updates map[string]interface{}) (*models.User, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, err
	}

	// Update allowed fields
	if age, ok := updates["age"]; ok {
		if ageInt, ok := age.(int); ok {
			user.Age = ageInt
		}
	}
	if gender, ok := updates["gender"]; ok {
		if genderStr, ok := gender.(string); ok {
			user.Gender = genderStr
		}
	}
	if bio, ok := updates["bio"]; ok {
		if bioStr, ok := bio.(string); ok {
			user.Bio = bioStr
		}
	}
	if orientation, ok := updates["sexual_orientation"]; ok {
		if orientationStr, ok := orientation.(string); ok {
			user.SexualOrientation = orientationStr
		}
	}
	if lookingFor, ok := updates["looking_for"]; ok {
		if lookingForStr, ok := lookingFor.(string); ok {
			user.LookingFor = lookingForStr
		}
	}

	if err := s.userRepo.Update(user); err != nil {
		return nil, err
	}

	return user, nil
}

func (s *UserService) UploadPhoto(userID uint, url string, order int) (*models.Photo, error) {
	photo := &models.Photo{
		UserID: userID,
		URL:    url,
		Order:  order,
	}

	if err := s.photoRepo.Create(photo); err != nil {
		return nil, err
	}

	return photo, nil
}

func (s *UserService) DeletePhoto(userID, photoID uint) error {
	photo, err := s.photoRepo.GetByID(photoID)
	if err != nil {
		return err
	}

	if photo.UserID != userID {
		return errors.New("unauthorized")
	}

	return s.photoRepo.Delete(photoID)
}

func (s *UserService) SetPrimaryPhoto(userID, photoID uint) error {
	photo, err := s.photoRepo.GetByID(photoID)
	if err != nil {
		return err
	}

	if photo.UserID != userID {
		return errors.New("unauthorized")
	}

	return s.photoRepo.SetPrimary(userID, photoID)
}

func (s *UserService) DiscoverUsers(userID uint, limit, offset int) ([]models.User, error) {
	return s.userRepo.GetDiscoverUsers(userID, limit, offset)
}
