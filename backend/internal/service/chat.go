package service

import (
	"errors"
	"time"

	"linda-dating-app/internal/models"
	"linda-dating-app/internal/repository"
)

type ChatService struct {
	messageRepo *repository.MessageRepository
	matchRepo   *repository.MatchRepository
}

func NewChatService(messageRepo *repository.MessageRepository, matchRepo *repository.MatchRepository) *ChatService {
	return &ChatService{
		messageRepo: messageRepo,
		matchRepo:   matchRepo,
	}
}

func (s *ChatService) GetConversations(userID uint) ([]models.Match, error) {
	return s.messageRepo.GetConversations(userID)
}

func (s *ChatService) GetMessages(userID, matchID uint, limit, offset int) ([]models.Message, error) {
	// Verify user is part of this match
	match, err := s.matchRepo.GetByID(matchID)
	if err != nil {
		return nil, err
	}

	if match.UserAID != userID && match.UserBID != userID {
		return nil, errors.New("unauthorized")
	}

	if !match.ChatEnabled {
		return nil, errors.New("chat is disabled for this match")
	}

	return s.messageRepo.GetByMatchID(matchID, limit, offset)
}

func (s *ChatService) SendMessage(senderID, matchID uint, content string) (*models.Message, error) {
	// Verify sender is part of this match
	match, err := s.matchRepo.GetByID(matchID)
	if err != nil {
		return nil, err
	}

	if match.UserAID != senderID && match.UserBID != senderID {
		return nil, errors.New("unauthorized")
	}

	if !match.ChatEnabled {
		return nil, errors.New("chat is disabled for this match")
	}

	// Determine receiver
	var receiverID uint
	if match.UserAID == senderID {
		receiverID = match.UserBID
	} else {
		receiverID = match.UserAID
	}

	// Create message
	message := &models.Message{
		MatchID:    matchID,
		SenderID:   senderID,
		ReceiverID: receiverID,
		Content:    content,
		Timestamp:  time.Now(),
		CreatedAt:  time.Now(),
	}

	if err := s.messageRepo.Create(message); err != nil {
		return nil, err
	}

	return message, nil
}

func (s *ChatService) MarkAsRead(userID, messageID uint) error {
	message, err := s.messageRepo.GetByID(messageID)
	if err != nil {
		return err
	}

	if message.ReceiverID != userID {
		return errors.New("unauthorized")
	}

	return s.messageRepo.MarkAsRead(messageID)
}

func (s *ChatService) GetUnreadCount(userID uint) (int64, error) {
	return s.messageRepo.GetUnreadCount(userID)
}
