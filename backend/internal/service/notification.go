package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"linda-dating-app/internal/models"
	"linda-dating-app/internal/repository"

	"github.com/redis/go-redis/v9"
)

type NotificationService struct {
	notificationRepo *repository.NotificationRepository
	redisClient      *redis.Client
}

func NewNotificationService(notificationRepo *repository.NotificationRepository, redisClient *redis.Client) *NotificationService {
	return &NotificationService{
		notificationRepo: notificationRepo,
		redisClient:      redisClient,
	}
}

func (s *NotificationService) CreateNotification(userID uint, message, notificationType string) (*models.Notification, error) {
	notification := &models.Notification{
		UserID:    userID,
		Message:   message,
		Type:      notificationType,
		CreatedAt: time.Now(),
	}

	if err := s.notificationRepo.Create(notification); err != nil {
		return nil, err
	}

	// Send real-time notification via Redis
	s.sendRealTimeNotification(userID, notification)

	return notification, nil
}

func (s *NotificationService) GetNotifications(userID uint, limit, offset int) ([]models.Notification, error) {
	return s.notificationRepo.GetByUserID(userID, limit, offset)
}

func (s *NotificationService) MarkAsRead(userID, notificationID uint) error {
	notification, err := s.notificationRepo.GetByID(notificationID)
	if err != nil {
		return err
	}

	if notification.UserID != userID {
		return errors.New("unauthorized")
	}

	return s.notificationRepo.MarkAsRead(notificationID)
}

func (s *NotificationService) DeleteNotification(userID, notificationID uint) error {
	notification, err := s.notificationRepo.GetByID(notificationID)
	if err != nil {
		return err
	}

	if notification.UserID != userID {
		return errors.New("unauthorized")
	}

	return s.notificationRepo.Delete(notificationID)
}

func (s *NotificationService) GetUnreadCount(userID uint) (int64, error) {
	return s.notificationRepo.GetUnreadCount(userID)
}

func (s *NotificationService) sendRealTimeNotification(userID uint, notification *models.Notification) {
	ctx := context.Background()
	channel := fmt.Sprintf("notifications:%d", userID)
	
	data, err := json.Marshal(notification)
	if err != nil {
		return
	}

	s.redisClient.Publish(ctx, channel, data)
}
