package handler

import (
	"net/http"
	"strconv"

	"linda-dating-app/internal/service"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService *service.UserService
	authService *service.AuthService
}

type UpdateProfileRequest struct {
	Age               *int    `json:"age,omitempty"`
	Gender            *string `json:"gender,omitempty"`
	Bio               *string `json:"bio,omitempty"`
	SexualOrientation *string `json:"sexual_orientation,omitempty"`
	LookingFor        *string `json:"looking_for,omitempty"`
}

type UploadPhotoRequest struct {
	URL   string `json:"url" binding:"required"`
	Order int    `json:"order"`
}

func NewUserHandler(userService *service.UserService, authService *service.AuthService) *UserHandler {
	return &UserHandler{
		userService: userService,
		authService: authService,
	}
}

func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	user, err := h.userService.GetProfile(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updates := make(map[string]interface{})
	if req.Age != nil {
		updates["age"] = *req.Age
	}
	if req.Gender != nil {
		updates["gender"] = *req.Gender
	}
	if req.Bio != nil {
		updates["bio"] = *req.Bio
	}
	if req.SexualOrientation != nil {
		updates["sexual_orientation"] = *req.SexualOrientation
	}
	if req.LookingFor != nil {
		updates["looking_for"] = *req.LookingFor
	}

	user, err := h.userService.UpdateProfile(userID, updates)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

func (h *UserHandler) UploadPhoto(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req UploadPhotoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	photo, err := h.userService.UploadPhoto(userID, req.URL, req.Order)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, photo)
}

func (h *UserHandler) DeletePhoto(c *gin.Context) {
	userID := c.GetUint("user_id")
	photoIDStr := c.Param("id")

	photoID, err := strconv.ParseUint(photoIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid photo ID"})
		return
	}

	if err := h.userService.DeletePhoto(userID, uint(photoID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Photo deleted successfully"})
}

func (h *UserHandler) SetPrimaryPhoto(c *gin.Context) {
	userID := c.GetUint("user_id")
	photoIDStr := c.Param("id")

	photoID, err := strconv.ParseUint(photoIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid photo ID"})
		return
	}

	if err := h.userService.SetPrimaryPhoto(userID, uint(photoID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Primary photo set successfully"})
}

func (h *UserHandler) DiscoverUsers(c *gin.Context) {
	userID := c.GetUint("user_id")

	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}

	users, err := h.userService.DiscoverUsers(userID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"users":  users,
		"limit":  limit,
		"offset": offset,
	})
}
