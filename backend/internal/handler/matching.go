package handler

import (
	"net/http"
	"strconv"

	"linda-dating-app/internal/service"

	"github.com/gin-gonic/gin"
)

type MatchingHandler struct {
	matchingService *service.MatchingService
	authService     *service.AuthService
}

type LikeRequest struct {
	LikedUserID uint `json:"liked_user_id" binding:"required"`
}

func NewMatchingHandler(matchingService *service.MatchingService, authService *service.AuthService) *MatchingHandler {
	return &MatchingHandler{
		matchingService: matchingService,
		authService:     authService,
	}
}

func (h *MatchingHandler) LikeUser(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req LikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	match, err := h.matchingService.LikeUser(userID, req.LikedUserID)
	if err != nil {
		c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{"liked": true}
	if match != nil {
		response["match"] = match
		response["is_match"] = true
	} else {
		response["is_match"] = false
	}

	c.JSON(http.StatusOK, response)
}

func (h *MatchingHandler) SuperlikeUser(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req LikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	match, err := h.matchingService.SuperlikeUser(userID, req.LikedUserID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response := gin.H{"superliked": true}
	if match != nil {
		response["match"] = match
		response["is_match"] = true
	} else {
		response["is_match"] = false
	}

	c.JSON(http.StatusOK, response)
}

func (h *MatchingHandler) PassUser(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req LikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.matchingService.PassUser(userID, req.LikedUserID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"passed": true})
}

func (h *MatchingHandler) GetMatches(c *gin.Context) {
	userID := c.GetUint("user_id")

	matches, err := h.matchingService.GetMatches(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"matches": matches})
}

func (h *MatchingHandler) UnmatchUser(c *gin.Context) {
	userID := c.GetUint("user_id")
	matchIDStr := c.Param("id")

	matchID, err := strconv.ParseUint(matchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
		return
	}

	if err := h.matchingService.UnmatchUser(userID, uint(matchID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Unmatched successfully"})
}
