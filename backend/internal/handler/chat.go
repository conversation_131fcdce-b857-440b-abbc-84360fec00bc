package handler

import (
	"net/http"
	"strconv"

	"linda-dating-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type ChatHandler struct {
	chatService *service.ChatService
	authService *service.AuthService
	upgrader    websocket.Upgrader
}

type SendMessageRequest struct {
	Content string `json:"content" binding:"required"`
}

func NewChatHandler(chatService *service.ChatService, authService *service.AuthService) *ChatHandler {
	return &ChatHandler{
		chatService: chatService,
		authService: authService,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for now
			},
		},
	}
}

func (h *ChatHandler) GetConversations(c *gin.Context) {
	userID := c.GetUint("user_id")

	conversations, err := h.chatService.GetConversations(userID)
	if err != nil {
		c.<PERSON><PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.<PERSON><PERSON>(http.StatusOK, gin.H{"conversations": conversations})
}

func (h *ChatHandler) GetMessages(c *gin.Context) {
	userID := c.GetUint("user_id")
	matchIDStr := c.Param("id")

	matchID, err := strconv.ParseUint(matchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 50
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}

	messages, err := h.chatService.GetMessages(userID, uint(matchID), limit, offset)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"messages": messages,
		"limit":    limit,
		"offset":   offset,
	})
}

func (h *ChatHandler) SendMessage(c *gin.Context) {
	userID := c.GetUint("user_id")
	matchIDStr := c.Param("id")

	matchID, err := strconv.ParseUint(matchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid match ID"})
		return
	}

	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	message, err := h.chatService.SendMessage(userID, uint(matchID), req.Content)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, message)
}

func (h *ChatHandler) MarkAsRead(c *gin.Context) {
	userID := c.GetUint("user_id")
	messageIDStr := c.Param("id")

	messageID, err := strconv.ParseUint(messageIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid message ID"})
		return
	}

	if err := h.chatService.MarkAsRead(userID, uint(messageID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Message marked as read"})
}

func (h *ChatHandler) HandleWebSocket(c *gin.Context) {
	// Get user ID from token
	token := c.Query("token")
	if token == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Token required"})
		return
	}

	_, err := h.authService.ValidateToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Upgrade connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		return
	}
	defer conn.Close()

	// Handle WebSocket connection
	for {
		var msg map[string]interface{}
		err := conn.ReadJSON(&msg)
		if err != nil {
			break
		}

		// Handle different message types
		msgType, ok := msg["type"].(string)
		if !ok {
			continue
		}

		switch msgType {
		case "typing":
			// Handle typing indicator
			// TODO: Broadcast typing status to other user
		case "message":
			// Handle new message
			// TODO: Process and broadcast message
		}
	}
}
