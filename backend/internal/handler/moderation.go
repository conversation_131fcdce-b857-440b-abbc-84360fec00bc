package handler

import (
	"net/http"
	"strconv"

	"linda-dating-app/internal/service"

	"github.com/gin-gonic/gin"
)

type ModerationHandler struct {
	moderationService *service.ModerationService
	authService       *service.AuthService
}

type CreateReportRequest struct {
	ReportedUserID uint   `json:"reported_user_id" binding:"required"`
	Reason         string `json:"reason" binding:"required"`
	Type           string `json:"type" binding:"required"`
}

type UpdateReportRequest struct {
	Status string `json:"status" binding:"required"`
}

func NewModerationHandler(moderationService *service.ModerationService, authService *service.AuthService) *ModerationHandler {
	return &ModerationHandler{
		moderationService: moderationService,
		authService:       authService,
	}
}

func (h *ModerationHandler) CreateReport(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req CreateReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	report, err := h.moderationService.CreateReport(userID, req.ReportedUserID, req.Reason, req.Type)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, report)
}

func (h *ModerationHandler) GetReports(c *gin.Context) {
	status := c.Query("status")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}

	reports, err := h.moderationService.GetReports(status, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"reports": reports,
		"limit":   limit,
		"offset":  offset,
	})
}

func (h *ModerationHandler) UpdateReport(c *gin.Context) {
	reportIDStr := c.Param("id")

	reportID, err := strconv.ParseUint(reportIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	var req UpdateReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	report, err := h.moderationService.UpdateReport(uint(reportID), req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, report)
}
