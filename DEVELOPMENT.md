# 🛠️ Vývojářská dokumentace - <PERSON> App

Kompletní průvodce pro vývojáře projektu Linda - od instalace až po produkční nasazení.

---

## 📋 Obsah

1. [Systémové požadavky](#-systémové-požadavky)
2. [Instalace Docker](#-instalace-docker)
3. [Klonování projektu](#-klonování-projektu)
4. [Python Virtual Environment](#-python-virtual-environment)
5. [Spuštění vývojového prostředí](#-spuštění-vývojového-prostředí)
6. [Docker Images - správa](#-docker-images---správa)
7. [Migrace na nový systém](#-migrace-na-nový-systém)
8. [VS Code konfigurace](#-vs-code-konfigurace)
9. [API dokumentace](#-api-dokumentace)
10. [Testován<PERSON>](#-testován<PERSON>)
11. [Debugging](#-debugging)
12. [Produk<PERSON><PERSON><PERSON> nasazen<PERSON>](#-produkční-nasazení)
13. [Časté problémy](#-časté-problémy)

---

## 🖥️ Systémové požadavky

### Minimální požadavky:
- **OS**: Linux (Ubuntu 20.04+), macOS 10.15+, Windows 10+
- **RAM**: 8 GB (doporučeno 16 GB)
- **Disk**: 20 GB volného místa
- **CPU**: 2 jádra (doporučeno 4+)

### Software:
- **Docker**: 24.0+
- **Docker Compose**: 2.20+
- **Git**: 2.30+
- **Node.js**: 18.0+ (pro frontend development)
- **Go**: 1.21+ (pro backend development)
- **Python**: 3.11+ (pro AI služby)

---

## 🐳 Instalace Docker

### Ubuntu/Debian:
```bash
# Aktualizace systému
sudo apt update && sudo apt upgrade -y

# Instalace závislostí
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Přidání Docker GPG klíče
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Přidání Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Instalace Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Přidání uživatele do docker skupiny
sudo usermod -aG docker $USER

# Restart pro aplikování změn
sudo systemctl enable docker
sudo systemctl start docker

# Ověření instalace
docker --version
docker compose version
```

### macOS:
```bash
# Pomocí Homebrew
brew install --cask docker

# Nebo stáhněte Docker Desktop z https://www.docker.com/products/docker-desktop
```

### Windows:
1. Stáhněte Docker Desktop z https://www.docker.com/products/docker-desktop
2. Spusťte installer a postupujte podle instrukcí
3. Restartujte počítač
4. Spusťte Docker Desktop

---

## 📥 Klonování projektu

### 1. Klonování z Git repository:
```bash
# HTTPS
git clone https://github.com/your-username/linda.git
cd linda

# SSH (doporučeno pro vývojáře)
<NAME_EMAIL>:your-username/linda.git
cd linda

# Ověření klonování
ls -la
```

### 2. Nastavení Git konfigurace:
```bash
# Globální konfigurace
git config --global user.name "Vaše Jméno"
git config --global user.email "<EMAIL>"

# Lokální konfigurace pro projekt
git config user.name "Vaše Jméno"
git config user.email "<EMAIL>"
```

### 3. Vytvoření vlastního branch:
```bash
# Vytvoření a přepnutí na nový branch
git checkout -b feature/your-feature-name

# Nebo pro bugfix
git checkout -b bugfix/issue-description
```

---

## 🐍 Python Virtual Environment

### Proč používat venv?
- **Izolace závislostí**: Každý projekt má své vlastní Python balíčky
- **Verze balíčků**: Různé projekty mohou používat různé verze stejných balíčků
- **Čistota systému**: Neznečišťuje systémový Python
- **Reprodukovatelnost**: Snadné sdílení přesných verzí závislostí

### Vytvoření a použití venv:

#### Linux/macOS:
```bash
# Přejděte do AI adresáře
cd ai

# Vytvoření virtual environment
python3 -m venv venv

# Aktivace venv
source venv/bin/activate

# Ověření aktivace (měl by se zobrazit (venv) před promptem)
which python
python --version

# Instalace závislostí
pip install --upgrade pip
pip install -r requirements.txt

# Deaktivace venv (když skončíte s prací)
deactivate
```

#### Windows:
```bash
# Přejděte do AI adresáře
cd ai

# Vytvoření virtual environment
python -m venv venv

# Aktivace venv
venv\Scripts\activate

# Ověření aktivace
where python
python --version

# Instalace závislostí
pip install --upgrade pip
pip install -r requirements.txt

# Deaktivace venv
deactivate
```

### Správa závislostí:
```bash
# Aktivace venv
source venv/bin/activate  # Linux/macOS
# nebo
venv\Scripts\activate     # Windows

# Přidání nového balíčku
pip install package-name

# Uložení aktuálních závislostí
pip freeze > requirements.txt

# Instalace ze souboru requirements.txt
pip install -r requirements.txt

# Zobrazení nainstalovaných balíčků
pip list

# Aktualizace balíčku
pip install --upgrade package-name
```

---

## 🚀 Spuštění vývojového prostředí

### 1. Rychlé spuštění (doporučeno):
```bash
# Spuštění všech služeb najednou
docker compose up -d --build

# Ověření běhu služeb
docker compose ps

# Zobrazení logů
docker compose logs -f
```

### 2. Postupné spuštění služeb:
```bash
# 1. Spuštění databáze a Redis
docker compose up -d db redis

# Čekání na inicializaci databáze (cca 30 sekund)
sleep 30

# 2. Spuštění backend API
docker compose up -d api

# 3. Spuštění AI služby
docker compose up -d ai

# 4. Spuštění frontendu
docker compose up -d frontend

# 5. Spuštění monitoringu (volitelné)
docker compose up -d prometheus grafana alertmanager
```

### 3. Vývojové spuštění (bez kontejnerů):

#### Backend (Go):
```bash
cd backend

# Instalace závislostí
go mod download

# Spuštění v development módu
go run cmd/server/main.go

# Nebo build a spuštění
go build -o bin/server cmd/server/main.go
./bin/server
```

#### Frontend (Vue.js):
```bash
cd frontend

# Instalace závislostí
npm install

# Development server s hot reload
npm run dev

# Build pro produkci
npm run build

# Preview produkčního buildu
npm run preview
```

#### AI Service (Python):
```bash
cd ai

# Aktivace venv
source venv/bin/activate  # Linux/macOS
# nebo
venv\Scripts\activate     # Windows

# Spuštění development serveru
python main.py

# Nebo s auto-reload
uvicorn main:app --reload --host 0.0.0.0 --port 5000
```

### 4. Ověření funkčnosti:

#### Kontrola služeb:
```bash
# Kontrola všech kontejnerů
docker compose ps

# Kontrola logů konkrétní služby
docker compose logs api
docker compose logs frontend
docker compose logs ai
docker compose logs db

# Kontrola zdraví služeb
curl http://localhost:8080/health    # Backend API
curl http://localhost:5000/health    # AI Service
curl http://localhost:3000           # Frontend
```

#### Testování API:
```bash
# Test registrace
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# Test přihlášení
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 5. Přístup k aplikaci:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **AI Service**: http://localhost:5000
- **Grafana**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Alertmanager**: http://localhost:9093

---

## 📦 Docker Images - správa

### Vytváření vlastních images:

#### Build všech images:
```bash
# Build všech služeb
docker compose build

# Build konkrétní služby
docker compose build api
docker compose build frontend
docker compose build ai

# Build s no-cache (čistý build)
docker compose build --no-cache
```

#### Tagging images:
```bash
# Tag pro produkci
docker tag linda-api:latest linda-api:v1.0.0
docker tag linda-frontend:latest linda-frontend:v1.0.0
docker tag linda-ai:latest linda-ai:v1.0.0

# Tag pro registry
docker tag linda-api:latest your-registry.com/linda-api:v1.0.0
```

### Zálohování images:

#### Export do tar souborů:
```bash
# Export jednotlivých images
docker save -o linda-api-backup.tar linda-api:latest
docker save -o linda-frontend-backup.tar linda-frontend:latest
docker save -o linda-ai-backup.tar linda-ai:latest

# Export všech images najednou
docker save -o linda-complete-backup.tar \
  linda-api:latest \
  linda-frontend:latest \
  linda-ai:latest

# Komprese zálohy
gzip linda-complete-backup.tar
```

#### Import ze zálohy:
```bash
# Import jednotlivého image
docker load -i linda-api-backup.tar

# Import komprimované zálohy
gunzip -c linda-complete-backup.tar.gz | docker load
```

### Push do registry:

#### Docker Hub:
```bash
# Přihlášení
docker login

# Push images
docker push your-username/linda-api:v1.0.0
docker push your-username/linda-frontend:v1.0.0
docker push your-username/linda-ai:v1.0.0
```

#### Privátní registry:
```bash
# Přihlášení do privátního registry
docker login your-registry.com

# Push images
docker push your-registry.com/linda-api:v1.0.0
docker push your-registry.com/linda-frontend:v1.0.0
docker push your-registry.com/linda-ai:v1.0.0
```

### Čištění Docker prostředí:
```bash
# Zastavení všech kontejnerů
docker compose down

# Odstranění nepoužívaných images
docker image prune -f

# Odstranění všech nepoužívaných objektů
docker system prune -a -f

# Odstranění volumes (POZOR: smaže data!)
docker volume prune -f

# Kompletní vyčištění (POZOR: smaže vše!)
docker system prune -a --volumes -f
```

---

## 🔄 Migrace na nový systém

### 1. Příprava na starém systému:

#### Zálohování databáze:
```bash
# Zálohování PostgreSQL databáze
docker compose exec db pg_dump -U postgres appdb > backup_$(date +%Y%m%d_%H%M%S).sql

# Nebo s kompresí
docker compose exec db pg_dump -U postgres appdb | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

# Zálohování s Docker volume
docker run --rm -v linda_pgdata:/data -v $(pwd):/backup alpine tar czf /backup/pgdata_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
```

#### Zálohování konfigurace:
```bash
# Kopírování konfiguračních souborů
cp docker-compose.yml docker-compose.yml.backup
cp -r .env .env.backup
cp -r monitoring/ monitoring_backup/

# Vytvoření kompletní zálohy projektu
tar czf linda_complete_backup_$(date +%Y%m%d_%H%M%S).tar.gz \
  --exclude=node_modules \
  --exclude=venv \
  --exclude=.git \
  .
```

#### Export Docker images:
```bash
# Export všech Linda images
docker save -o linda_images_$(date +%Y%m%d_%H%M%S).tar \
  $(docker images --format "table {{.Repository}}:{{.Tag}}" | grep linda | tr '\n' ' ')

# Komprese
gzip linda_images_*.tar
```

### 2. Instalace na novém systému:

#### Přenos souborů:
```bash
# Pomocí SCP
scp linda_complete_backup_*.tar.gz user@new-server:/home/<USER>/
scp linda_images_*.tar.gz user@new-server:/home/<USER>/
scp backup_*.sql.gz user@new-server:/home/<USER>/

# Pomocí rsync
rsync -avz --exclude=node_modules --exclude=venv --exclude=.git \
  /path/to/linda/ user@new-server:/home/<USER>/linda/
```

#### Rozbalení na novém systému:
```bash
# Rozbalení projektu
tar xzf linda_complete_backup_*.tar.gz

# Import Docker images
gunzip linda_images_*.tar.gz
docker load -i linda_images_*.tar

# Rozbalení databázové zálohy
gunzip backup_*.sql.gz
```

### 3. Obnovení na novém systému:

#### Spuštění základních služeb:
```bash
cd linda

# Spuštění databáze a Redis
docker compose up -d db redis

# Čekání na inicializaci
sleep 30
```

#### Obnovení databáze:
```bash
# Obnovení z SQL zálohy
docker compose exec -T db psql -U postgres appdb < backup_*.sql

# Nebo pomocí Docker volume (pokud máte volume zálohu)
docker run --rm -v linda_pgdata:/data -v $(pwd):/backup alpine \
  tar xzf /backup/pgdata_backup_*.tar.gz -C /data
```

#### Spuštění všech služeb:
```bash
# Spuštění všech služeb
docker compose up -d

# Ověření funkčnosti
docker compose ps
curl http://localhost:8080/health
curl http://localhost:5000/health
```

### 4. Ověření migrace:

#### Kontrola dat:
```bash
# Připojení k databázi
docker compose exec db psql -U postgres appdb

# Kontrola tabulek
\dt

# Kontrola počtu záznamů
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM messages;

# Ukončení
\q
```

#### Funkční testy:
```bash
# Test API endpointů
curl http://localhost:8080/api/v1/health
curl http://localhost:5000/health

# Test registrace
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"migrationtest","email":"<EMAIL>","password":"test123"}'
```

---

## 🔧 VS Code konfigurace

### Konfigurace pro Augment Code agenta

Pro optimální práci s Augment Code agentem vytvořte soubor `.vscode/settings.json`:

#### 1. Vytvoření .vscode/settings.json:
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.workingDirectories": ["frontend"],
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.server.hybridMode": true,
  "go.toolsManagement.autoUpdate": true,
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.terminal.activateEnvironment": true,
  "files.associations": {
    "*.vue": "vue",
    "docker-compose*.yml": "dockercompose"
  },
  "emmet.includeLanguages": {
    "vue": "html"
  },
  "augment.projectContext": {
    "name": "Linda Dating App",
    "description": "Moderní seznamovací aplikace s Vue.js frontendem, Go backendem a AI funkcemi",
    "technologies": ["Vue.js", "Go", "Python", "PostgreSQL", "Redis", "Docker"],
    "architecture": "microservices"
  }
}
```

#### 2. Doporučená rozšíření pro VS Code:
Vytvořte soubor `.vscode/extensions.json`:
```json
{
  "recommendations": [
    "vue.volar",
    "golang.go",
    "ms-python.python",
    "ms-vscode.vscode-docker",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json"
  ]
}
```

#### 3. Workspace nastavení:
Vytvořte soubor `.vscode/tasks.json` pro rychlé úkoly:
```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Development",
      "type": "shell",
      "command": "docker-compose up -d",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "Stop Development",
      "type": "shell",
      "command": "docker-compose down",
      "group": "build"
    },
    {
      "label": "Frontend Dev",
      "type": "shell",
      "command": "npm run dev",
      "options": {
        "cwd": "${workspaceFolder}/frontend"
      },
      "group": "build"
    },
    {
      "label": "Backend Dev",
      "type": "shell",
      "command": "go run cmd/main.go",
      "options": {
        "cwd": "${workspaceFolder}/backend"
      },
      "group": "build"
    }
  ]
}
```

---

## 🌐 API dokumentace

### Autentizace endpointy:
```bash
# Registrace nového uživatele
POST /api/v1/auth/register
Content-Type: application/json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "age": 25,
  "gender": "male"
}

# Přihlášení
POST /api/v1/auth/login
Content-Type: application/json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}

# Obnovení tokenu
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>

# Odhlášení
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

### Uživatelské profily:
```bash
# Získání vlastního profilu
GET /api/v1/users/profile
Authorization: Bearer <access_token>

# Aktualizace profilu
PUT /api/v1/users/profile
Authorization: Bearer <access_token>
Content-Type: application/json
{
  "bio": "Miluji cestování a fotografii",
  "interests": ["cestování", "fotografie", "sport"],
  "looking_for": "serious_relationship"
}

# Nahrání fotky
POST /api/v1/users/photos
Authorization: Bearer <access_token>
Content-Type: multipart/form-data
photo: <file>
is_primary: true
```

### Objevování a matching:
```bash
# Získání návrhů uživatelů
GET /api/v1/users/discover?limit=10&age_min=20&age_max=35
Authorization: Bearer <access_token>

# Lajkování uživatele
POST /api/v1/matching/like
Authorization: Bearer <access_token>
Content-Type: application/json
{
  "user_id": 123,
  "type": "like"  # nebo "superlike"
}

# Přeskočení uživatele
POST /api/v1/matching/pass
Authorization: Bearer <access_token>
Content-Type: application/json
{
  "user_id": 123
}

# Seznam matchů
GET /api/v1/matching/matches
Authorization: Bearer <access_token>
```

### Chat a zprávy:
```bash
# Seznam konverzací
GET /api/v1/chat/conversations
Authorization: Bearer <access_token>

# Historie zpráv
GET /api/v1/chat/conversations/123/messages?limit=50&offset=0
Authorization: Bearer <access_token>

# Poslání zprávy
POST /api/v1/chat/conversations/123/messages
Authorization: Bearer <access_token>
Content-Type: application/json
{
  "content": "Ahoj! Jak se máš?"
}

# Označení zpráv jako přečtené
PUT /api/v1/chat/conversations/123/read
Authorization: Bearer <access_token>
```

---

## 🧪 Testování

### Backend testy (Go):
```bash
cd backend

# Spuštění všech testů
go test ./...

# Testy s verbose výstupem
go test -v ./...

# Testy s coverage
go test -cover ./...

# Testy konkrétního balíčku
go test ./internal/handlers

# Benchmark testy
go test -bench=. ./...

# Race condition detection
go test -race ./...
```

### Frontend testy (Vue.js):
```bash
cd frontend

# Unit testy
npm run test

# Testy s watch módem
npm run test:watch

# Coverage report
npm run test:coverage

# E2E testy (pokud jsou nakonfigurovány)
npm run test:e2e

# Linting
npm run lint

# Type checking (pokud používáte TypeScript)
npm run type-check
```

### AI Service testy (Python):
```bash
cd ai

# Aktivace venv
source venv/bin/activate

# Spuštění testů
python -m pytest

# Testy s verbose výstupem
python -m pytest -v

# Coverage report
python -m pytest --cov=.

# Testy konkrétního souboru
python -m pytest tests/test_recommendations.py

# Testy s markers
python -m pytest -m "not slow"
```

### Integrační testy:
```bash
# Test celého workflow
./scripts/integration_test.sh

# Nebo manuálně:
# 1. Registrace
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"test123","age":25,"gender":"male"}'

# 2. Přihlášení a získání tokenu
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}' | jq -r '.access_token')

# 3. Test profilu
curl -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/v1/users/profile

# 4. Test objevování
curl -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/v1/users/discover
```

---

## 🐛 Debugging

### Logy aplikace:
```bash
# Zobrazení logů všech služeb
docker compose logs -f

# Logy konkrétní služby
docker compose logs -f api
docker compose logs -f frontend
docker compose logs -f ai
docker compose logs -f db

# Logy s časovými razítky
docker compose logs -f -t api

# Posledních N řádků logů
docker compose logs --tail=100 api
```

### Debugging backend (Go):
```bash
cd backend

# Spuštění s debug informacemi
go run -race cmd/server/main.go

# Použití Delve debuggeru
go install github.com/go-delve/delve/cmd/dlv@latest
dlv debug cmd/server/main.go

# Profiling
go tool pprof http://localhost:8080/debug/pprof/profile
```

### Debugging frontend (Vue.js):
```bash
cd frontend

# Development server s debug módem
npm run dev

# Analýza bundle velikosti
npm run build:analyze

# Vue DevTools v prohlížeči
# Nainstalujte Vue DevTools extension
```

### Debugging AI služby (Python):
```bash
cd ai

# Spuštění s debug módem
python -m pdb main.py

# Nebo s debugpy pro VS Code
pip install debugpy
python -m debugpy --listen 5678 --wait-for-client main.py
```

### Debugging databáze:
```bash
# Připojení k PostgreSQL
docker compose exec db psql -U postgres appdb

# Zobrazení aktivních spojení
SELECT * FROM pg_stat_activity;

# Zobrazení pomalých dotazů
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

# Analýza velikosti tabulek
SELECT schemaname,tablename,attname,n_distinct,correlation FROM pg_stats;
```

### Monitoring a metriky:
```bash
# Prometheus metriky
curl http://localhost:9090/api/v1/query?query=up

# Grafana dashboards
# Přístup: http://localhost:3001 (admin/admin)

# Aplikační metriky
curl http://localhost:8080/metrics
curl http://localhost:5000/metrics
```

---

## 🚀 Produkční nasazení

### Příprava produkčního prostředí:

#### 1. Environment proměnné:
```bash
# Vytvoření .env.production
cat > .env.production << EOF
# Database
DATABASE_URL=**********************************************/linda_prod?sslmode=require

# Redis
REDIS_URL=redis:6379

# JWT
JWT_SECRET=$(openssl rand -base64 32)
JWT_REFRESH_SECRET=$(openssl rand -base64 32)

# API
PORT=8080
ENVIRONMENT=production
DEBUG=false

# AI Service
AI_SERVICE_URL=http://ai:5000
OPENAI_API_KEY=your-production-openai-key

# Security
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 16)
EOF
```

#### 2. Docker Compose pro produkci:
```yaml
# docker-compose.prod.yml
version: '3.9'

services:
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    networks:
      - appnet

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    depends_on:
      - api
    networks:
      - appnet

  ai:
    build:
      context: ./ai
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - db
    networks:
      - appnet

  db:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    volumes:
      - pgdata_prod:/var/lib/postgresql/data
    networks:
      - appnet

  redis:
    image: redis:7
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_prod:/data
    networks:
      - appnet

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - api
    networks:
      - appnet

networks:
  appnet:
    driver: bridge

volumes:
  pgdata_prod:
  redis_prod:
```

#### 3. Nginx konfigurace:
```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream api {
        server api:8080;
    }

    upstream frontend {
        server frontend:80;
    }

    # HTTP redirect to HTTPS
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # API
        location /api/ {
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket support
        location /ws/ {
            proxy_pass http://api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

#### 4. SSL certifikáty:
```bash
# Pomocí Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Získání certifikátu
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Automatické obnovení
sudo crontab -e
# Přidat: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Deployment:

#### 1. Build a deploy:
```bash
# Nastavení produkčních proměnných
export $(cat .env.production | xargs)

# Build produkčních images
docker compose -f docker-compose.prod.yml build

# Spuštění v produkci
docker compose -f docker-compose.prod.yml up -d

# Ověření
docker compose -f docker-compose.prod.yml ps
```

#### 2. Databázové migrace:
```bash
# Spuštění migrací
docker compose -f docker-compose.prod.yml exec api ./migrate up

# Nebo manuálně
docker compose -f docker-compose.prod.yml exec db psql -U postgres -d linda_prod -f /migrations/init.sql
```

#### 3. Monitoring v produkci:
```bash
# Spuštění monitoring stacku
docker compose -f docker-compose.prod.yml up -d prometheus grafana alertmanager

# Import Grafana dashboards
curl -X POST http://admin:${GRAFANA_ADMIN_PASSWORD}@localhost:3001/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @monitoring/grafana-dashboard.json
```

---

## 🚨 Časté problémy

### Docker problémy:

#### Port už je používán:
```bash
# Najít proces na portu
sudo lsof -i :8080
sudo netstat -tulpn | grep :8080

# Ukončit proces
sudo kill -9 <PID>

# Nebo změnit port v docker-compose.yml
```

#### Nedostatek místa na disku:
```bash
# Vyčištění Docker objektů
docker system prune -a -f

# Vyčištění volumes
docker volume prune -f

# Kontrola velikosti
docker system df
```

#### Problémy s permissions:
```bash
# Oprava ownership
sudo chown -R $USER:$USER .

# Oprava Docker socket permissions
sudo chmod 666 /var/run/docker.sock
```

### Databázové problémy:

#### Připojení k databázi selhává:
```bash
# Kontrola běhu kontejneru
docker compose ps db

# Kontrola logů
docker compose logs db

# Test připojení
docker compose exec db psql -U postgres -c "SELECT 1;"
```

#### Pomalé dotazy:
```sql
-- Analýza pomalých dotazů
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Vytvoření indexů
CREATE INDEX CONCURRENTLY idx_users_age ON users(age);
CREATE INDEX CONCURRENTLY idx_messages_timestamp ON messages(timestamp);
```

### Frontend problémy:

#### Build selhává:
```bash
# Vyčištění cache
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Kontrola Node.js verze
node --version
npm --version
```

#### CORS chyby:
```javascript
// Kontrola CORS nastavení v backend
// backend/internal/middleware/cors.go
```

### Performance problémy:

#### Vysoké využití CPU:
```bash
# Monitoring procesů
docker stats

# Profiling Go aplikace
go tool pprof http://localhost:8080/debug/pprof/profile

# Optimalizace databáze
VACUUM ANALYZE;
REINDEX DATABASE linda_prod;
```

#### Vysoké využití paměti:
```bash
# Monitoring paměti
free -h
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"

# Optimalizace Redis
redis-cli CONFIG SET maxmemory 256mb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

---

## 📞 Podpora a kontakt

### Dokumentace:
- **Projekt README**: [README.md](README.md)
- **API dokumentace**: http://localhost:8080/docs (Swagger)
- **Grafana dashboards**: http://localhost:3001

### Užitečné odkazy:
- **Go dokumentace**: https://golang.org/doc/
- **Vue.js dokumentace**: https://vuejs.org/guide/
- **Docker dokumentace**: https://docs.docker.com/
- **PostgreSQL dokumentace**: https://www.postgresql.org/docs/

### Troubleshooting:
1. Zkontrolujte logy: `docker compose logs -f`
2. Ověřte síťové připojení: `docker network ls`
3. Zkontrolujte porty: `netstat -tulpn`
4. Restartujte služby: `docker compose restart`

---

*Vytvořeno s ❤️ pro Linda Dating App vývojáře*
